package com.keyway.adapters.handlers.rest.historical

import com.keyway.adapters.dtos.metrics.historical.HistoricalDaysOnMarketSummary
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.utils.HandlersUtils.calculate
import com.keyway.adapters.handlers.utils.HandlersUtils.fixToMaxRange
import com.keyway.core.dto.historical.HistoricalDaysOnMarketInput
import com.keyway.core.dto.historical.HistoricalDaysOnMarketSummaryDto
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.usecases.historical.GetHistoricalDaysOnMarketUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import java.time.LocalDate

class GetHistoricalDaysOnMarketHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getHistoricalDaysOnMarket: GetHistoricalDaysOnMarketUseCase,
) {
    suspend operator fun invoke(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity?,
        bedrooms: Int?,
    ): HistoricalDaysOnMarketSummary =
        HistoricalDaysOnMarketInput(
            historicalInput =
                HistoricalInput(
                    ids = ids,
                    idType = idType,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = periodicity.calculate(dateFrom, dateTo),
                ),
            bedrooms = bedrooms,
        ).let { input ->
            useCaseExecutor(
                useCase = getHistoricalDaysOnMarket,
                inputDto = input,
                inputConverter = { input -> input.copy(historicalInput = input.historicalInput.fixToMaxRange()) },
                outputConverter = { output ->
                    output
                        .mapTo<HistoricalDaysOnMarketSummaryDto, HistoricalDaysOnMarketSummary>()
                },
            )
        }
}

package com.keyway.adapters.handlers.rest.units

import com.keyway.adapters.converters.dto.PropertiesListingConverter
import com.keyway.adapters.converters.dto.PropertiesListingConverter.toResponse
import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.usecases.listings.GetPropertiesLastListingUseCase

class GetPropertiesLastListingHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getPropertiesLastListingUseCase: GetPropertiesLastListingUseCase,
) {
    suspend fun invoke(input: PropertiesListingDataInput): List<RentListingsResponse> =
        useCaseExecutor.invoke(
            useCase = getPropertiesLastListingUseCase,
            inputDto = input,
            outputConverter = { buildResponse(it) },
            inputConverter = { PropertiesListingConverter.toGetPropertiesListingsInput(it) },
        )

    private fun buildResponse(output: Map<String, List<RentListingWithEffectiveRentOutput>>): List<RentListingsResponse> =
        output.map { (propertyId, unitRentListings) ->
            RentListingsResponse(
                propertyId = propertyId,
                units =
                    unitRentListings.map { unitRentListing ->
                        toResponse(unitRentListing.rentListing, unitRentListing.effectiveRents)
                    },
            )
        }
}

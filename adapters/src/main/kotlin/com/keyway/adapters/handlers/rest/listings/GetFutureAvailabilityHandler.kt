package com.keyway.adapters.handlers.rest.listings

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.usecases.listings.GetZipAndMsaFutureAvailabilityUseCase

class GetFutureAvailabilityHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getZipAndMsaFutureAvailabilityUseCase: GetZipAndMsaFutureAvailabilityUseCase,
) {
    suspend operator fun invoke(
        id: String,
        idType: IdType,
    ): List<BedroomFutureAvailability> =
        useCaseExecutor.invoke(
            useCase = getZipAndMsaFutureAvailabilityUseCase,
            inputDto = GetZipAndMsaFutureAvailabilityUseCase.Input(id, idType),
            outputConverter = { it },
            inputConverter = { it },
        )
}

package com.keyway.adapters.handlers.rest.historical

import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.adapters.handlers.utils.HandlersUtils.calculate
import com.keyway.adapters.handlers.utils.HandlersUtils.fixToMaxRange
import com.keyway.core.dto.HistoricalExposure
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.usecases.historical.GetHistoricalExposureUseCase
import java.time.LocalDate

class GetHistoricalExposureByIdsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val getHistoricalExposureUseCase: GetHistoricalExposureUseCase,
) {
    suspend operator fun invoke(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity?,
    ): HistoricalExposure =
        useCaseExecutor(
            useCase = getHistoricalExposureUseCase,
            inputDto =
                HistoricalInput(
                    ids = ids,
                    idType = idType,
                    dateFrom = dateFrom,
                    dateTo = dateTo,
                    periodicity = periodicity.calculate(dateFrom, dateTo),
                ),
            inputConverter = { it.fixToMaxRange() },
            outputConverter = { it },
        )
}

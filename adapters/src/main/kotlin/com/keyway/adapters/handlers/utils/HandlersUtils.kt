package com.keyway.adapters.handlers.utils

import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.entities.HistoricalPeriodicity
import java.time.LocalDate
import java.time.temporal.ChronoUnit

object HandlersUtils {
    const val MAX_PROPERTIES_ALLOWED = 150
    const val MIN_PROPERTIES_ALLOWED = 1
    const val PROPERTIES_DELIMITER = ","
    const val MAX_DAILY_AMOUNT = 45L
    const val MAX_WEEKLY_AMOUNT = 30L
    const val MAX_MONTHLY_AMOUNT = 30L

    fun HistoricalPeriodicity?.calculate(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): HistoricalPeriodicity =
        this
            ?: (dateFrom.until(dateTo, ChronoUnit.DAYS)).let {
                when {
                    it > MAX_WEEKLY_AMOUNT * 7 -> HistoricalPeriodicity.MONTHLY
                    it > MAX_DAILY_AMOUNT -> HistoricalPeriodicity.WEEKLY
                    else -> HistoricalPeriodicity.DAILY
                }
            }

    fun HistoricalInput.fixToMaxRange(): HistoricalInput =
        when (this.periodicity) {
            HistoricalPeriodicity.DAILY ->
                this.dateTo.minusDays(MAX_DAILY_AMOUNT)
            HistoricalPeriodicity.WEEKLY ->
                this.dateTo.minusWeeks(MAX_WEEKLY_AMOUNT)
            HistoricalPeriodicity.MONTHLY ->
                this.dateTo.minusMonths(MAX_MONTHLY_AMOUNT)
        }.takeIf { this.dateFrom <= it }
            ?.let { this.copy(dateFrom = it) }
            ?: this
}

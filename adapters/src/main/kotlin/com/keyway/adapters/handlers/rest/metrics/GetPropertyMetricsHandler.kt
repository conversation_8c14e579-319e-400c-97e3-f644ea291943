package com.keyway.adapters.handlers.rest.metrics

import com.keyway.adapters.dtos.metrics.property.BedroomRentSummary
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentSummary
import com.keyway.adapters.dtos.metrics.property.PropertyRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitMixRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitsRentSummary
import com.keyway.adapters.executor.UseCaseExecutor
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.input.ComputeAggregatedMetricInput
import com.keyway.core.dto.listings.output.RentMetricOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.usecases.metrics.ComputeAggregatedMetricUseCase
import com.keyway.kommons.mapper.dataclass.mapTo
import java.time.LocalDate

class GetPropertyMetricsHandler(
    private val useCaseExecutor: UseCaseExecutor,
    private val computeAggregatedMetricUseCase: ComputeAggregatedMetricUseCase,
) {
    suspend operator fun invoke(input: Input): List<Any> =
        useCaseExecutor.invoke(
            useCase = computeAggregatedMetricUseCase,
            inputDto = input,
            outputConverter = { buildResponse(it) },
            inputConverter = { buildInput(it) },
        )

    private fun buildInput(it: Input): ComputeAggregatedMetricInput =
        ComputeAggregatedMetricInput(
            ids = it.propertyIds,
            idType = IdType.PROPERTY,
            dateFrom = it.dateFrom,
            dateTo = it.dateTo,
            type = AggregatedMetricType.valueOf(it.metricType.toString()),
            unitCondition = it.unitCondition,
        )

    private fun RentMetricOutput.getAdditions() =
        mapOf(
            "propertyId" to this.id,
        )

    private fun buildResponse(result: List<RentMetricOutput>): List<Any> =
        result.map {
            when (it.type) {
                AggregatedMetricType.UNIT_MIX ->
                    it.mapTo<RentMetricOutput, UnitMixRentSummary>(additions = it.getAdditions())
                AggregatedMetricType.FLOOR_PLAN ->
                    it.mapTo<RentMetricOutput, FloorPlanRentSummary>(additions = it.getAdditions())
                AggregatedMetricType.BEDROOMS ->
                    it.mapTo<RentMetricOutput, BedroomRentSummary>(additions = it.getAdditions())
                AggregatedMetricType.BY_ID ->
                    it.mapTo<RentMetricOutput, PropertyRentSummary>(additions = it.getAdditions())
                AggregatedMetricType.UNITS ->
                    it.mapTo<RentMetricOutput, UnitsRentSummary>(additions = it.getAdditions())
            }
        }

    data class Input(
        val propertyIds: Set<String>,
        val dateFrom: LocalDate?,
        val dateTo: LocalDate?,
        val metricType: MetricType,
        val unitCondition: UnitCondition?,
    )
}

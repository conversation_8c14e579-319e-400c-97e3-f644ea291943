package com.keyway.adapters.exceptions

import org.apache.hc.core5.http.HttpStatus
import org.apache.hc.core5.http.impl.EnglishReasonPhraseCatalog
import java.util.Locale

open class ForbiddenException(
    message: String = EnglishReasonPhraseCatalog.INSTANCE.getReason(HttpStatus.SC_FORBIDDEN, Locale.ENGLISH),
    httpStatusCode: Int = HttpStatus.SC_FORBIDDEN,
    errorCode: String = "FORBIDDEN",
    cause: Throwable? = null,
) : RestException(
        message = message,
        httpStatusCode = httpStatusCode,
        errorCode = errorCode,
        statusCode = HttpStatus.SC_FORBIDDEN,
        cause = cause,
    )

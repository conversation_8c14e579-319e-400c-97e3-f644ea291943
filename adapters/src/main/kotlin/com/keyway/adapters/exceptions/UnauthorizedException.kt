package com.keyway.adapters.exceptions

import org.apache.hc.core5.http.HttpStatus
import org.apache.hc.core5.http.impl.EnglishReasonPhraseCatalog
import java.util.Locale

open class UnauthorizedException(
    message: String = EnglishReasonPhraseCatalog.INSTANCE.getReason(HttpStatus.SC_UNAUTHORIZED, Locale.ENGLISH),
    httpStatusCode: Int = HttpStatus.SC_UNAUTHORIZED,
    errorCode: String = "UNAUTHORIZED",
    cause: Throwable? = null,
) : RestException(
        message = message,
        httpStatusCode = httpStatusCode,
        errorCode = errorCode,
        statusCode = HttpStatus.SC_UNAUTHORIZED,
        cause = cause,
    )

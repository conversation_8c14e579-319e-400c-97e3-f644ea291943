package com.keyway.adapters.exceptions

import org.apache.hc.core5.http.HttpStatus
import org.apache.hc.core5.http.impl.EnglishReasonPhraseCatalog
import java.util.Locale

open class InternalServerException(
    message: String = EnglishReasonPhraseCatalog.INSTANCE.getReason(HttpStatus.SC_INTERNAL_SERVER_ERROR, Locale.ENGLISH),
    httpStatusCode: Int = HttpStatus.SC_INTERNAL_SERVER_ERROR,
    errorCode: String = "INTERNAL_SERVER_ERROR",
    cause: Throwable? = null,
) : RestException(
        message = message,
        httpStatusCode = httpStatusCode,
        errorCode = errorCode,
        statusCode = HttpStatus.SC_INTERNAL_SERVER_ERROR,
        cause = cause,
    )

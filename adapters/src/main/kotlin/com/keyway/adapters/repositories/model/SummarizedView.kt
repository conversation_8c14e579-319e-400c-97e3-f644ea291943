package com.keyway.adapters.repositories.model

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.ViewsUtils
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.utils.DateUtils.equalMonthYear
import java.time.LocalDate

data class SummarizedView(
    val date: LocalDate,
    val view: ViewsUtils.MaterializedViews,
) {
    val viewName = view.getViewName(date)

    fun getCondition(
        idType: IdType,
        id: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} = '$id'
                ${getDateOfRecordCondition(dateFrom, dateTo)} 
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()

    fun getCondition(
        idType: IdType,
        ids: Set<String>,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int? = null,
    ): String =
        """ 
                WHERE ${idType.getSqlColumn()} IN (${ids.joinToString(",") { "'$it'" }})
                ${getDateOfRecordCondition(dateFrom, dateTo)} 
                ${bedrooms?.let { " AND bedrooms = $it " } ?: ""}
        """.trimMargin()

    private fun getDateOfRecordCondition(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): String =
        when {
            date.equalMonthYear(dateFrom) && date.equalMonthYear(dateTo) -> "  AND date_of_record BETWEEN '$dateFrom' AND '$dateTo' "
            date.equalMonthYear(dateFrom) -> "  AND date_of_record >= '$dateFrom' "
            date.equalMonthYear(dateTo) -> "  AND date_of_record <= '$dateTo' "
            else -> ""
        }
}

package com.keyway.adapters.repositories.model

import com.fasterxml.jackson.databind.JsonNode
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListingType
import com.keyway.kommons.db.mapper.DatabaseMapper
import com.keyway.kommons.mapper.type.JacksonComplexType
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class EffectiveRentDBModel(
    val id: String,
    val rentListingId: String,
    val concessionIds: JsonNode,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val rent: BigDecimal,
    val rentDeposit: BigDecimal?,
    val concessions: String,
    val createdAt: OffsetDateTime,
    val updateAt: OffsetDateTime,
    val isActive: Boolean,
    val recordSource: String?,
    val zipCode: String?,
    val msaCode: String?,
    val unitSquareFootage: BigDecimal?,
    val bedrooms: Int?,
    val bathrooms: BigDecimal?,
    val floorPlan: String?,
    val availableIn: LocalDate?,
    val propertyId: String?,
    val type: String?,
    val typeId: String?,
) {
    fun toEffectiveRent(): EffectiveRent =
        EffectiveRent(
            id = this.id,
            rentListingId = this.rentListingId,
            dateFrom = this.dateFrom,
            dateTo = this.dateTo,
            rent = Money.of(this.rent),
            rentDeposit = this.rentDeposit?.let { Money.of(it) },
            concessions = this.concessions,
            createdAt = this.createdAt,
            updateAt = this.updateAt,
            concessionIds =
                DatabaseMapper.decode(
                    this.concessionIds.toString(),
                    object : JacksonComplexType<List<String>>() {},
                ),
            isActive = this.isActive,
            recordSource = this.recordSource,
            zipCode = this.zipCode,
            msaCode = this.msaCode,
            unitSquareFootage = this.unitSquareFootage,
            bedroomsQuantity = this.bedrooms,
            bathroomsQuantity = this.bathrooms,
            floorPlan = this.floorPlan,
            propertyId = propertyId,
            type = type?.let { RentListingType.valueOf(type) },
            typeId = typeId,
        )
}

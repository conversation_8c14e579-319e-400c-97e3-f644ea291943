package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.model.MultifamilyPropertyModel
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.setNullableBoolean
import com.keyway.adapters.repositories.utils.setNullableInt
import com.keyway.core.entities.MultifamilyProperty
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.kommons.db.SqlClient
import java.time.Clock
import java.time.OffsetDateTime
import kotlin.collections.emptyList

class MultifamilyPropertyPostgresRepository(
    private val sqlClient: SqlClient,
    private val clock: Clock,
) : MultifamilyPropertyRepository {
    override fun saveOrUpdate(property: MultifamilyProperty) {
        val now = OffsetDateTime.now(clock)
        sqlClient.update(
            insertProperty,
        ) { ps ->
            var parameterIndex = 1
            ps.setString(parameterIndex++, property.id)
            ps.setString(parameterIndex++, property.address)
            ps.setString(parameterIndex++, property.city)
            ps.setString(parameterIndex++, property.county)
            ps.setLong(parameterIndex++, property.zipCode)
            ps.setString(parameterIndex++, property.state)
            ps.setBigDecimal(parameterIndex++, property.location.latitude)
            ps.setBigDecimal(parameterIndex++, property.location.longitude)
            ps.setBigDecimal(parameterIndex++, property.squareFootage)
            ps.setBigDecimal(parameterIndex++, property.squareFootagePerUnit)
            ps.setString(parameterIndex++, property.sourceType)
            ps.setObject(parameterIndex++, property.tractCode)
            ps.setObject(parameterIndex++, property.constructionYear)
            ps.setObject(parameterIndex++, property.renovationYear)
            ps.setInt(parameterIndex++, property.unitQuantity)
            ps.setBigDecimal(parameterIndex++, property.occupancyPercentage)
            ps.setBoolean(parameterIndex++, property.isActive)
            ps.setObject(parameterIndex++, now)
            ps.setObject(parameterIndex++, now)
            ps.setArray(parameterIndex++, ps.connection.createArrayOf("VARCHAR", property.propertyAmenities.toTypedArray()))
            ps.setArray(parameterIndex++, ps.connection.createArrayOf("VARCHAR", property.unitsAmenities.toTypedArray()))
            // For geolocation parameter (longitude, latitude for ST_MakePoint)
            ps.setBigDecimal(parameterIndex++, property.location.longitude)
            ps.setBigDecimal(parameterIndex++, property.location.latitude)
            ps.setBigDecimal(parameterIndex++, property.qualityOverallScore)
            ps.setNullableInt(parameterIndex++, property.stories)
            ps.setString(parameterIndex++, property.propertyStyle)
            ps.setArray(parameterIndex++, ps.connection.createArrayOf("VARCHAR", property.housingSegment.toTypedArray()))
            ps.setNullableBoolean(parameterIndex++, property.hasAffordableUnits)
        }
    }

    override fun getByPropertyId(id: String): MultifamilyProperty =
        sqlClient
            .getOneOrFail(
                query = "SELECT * FROM multifamily_properties WHERE id = ? ",
                params = listOf(id),
                messageKey = "PROPERTY NOT FOUND",
                clazz = MultifamilyPropertyModel::class.java,
            ).toMultifamilyProperty()

    override fun getByPropertyUnitsByIds(id: Set<String>): Map<String, Int> =
        sqlClient
            .getAll(
                query = """SELECT id, unit_quantity::BIGINT FROM multifamily_properties
                     WHERE id IN (${id.joinToString(", ") { "'$it'" }}) """,
                params = emptyList(),
            ) {
                it.getString("id") to it.getInt("unit_quantity")
            }.toMap()

    private val insertProperty =
        """
        INSERT INTO multifamily_properties (
            id,
            address,
            city,
            county,
            zip_code,
            state,
            latitude,
            longitude,
            square_footage,
            square_footage_per_unit,
            source_type,
            tract_code,
            construction_year,
            renovation_year,
            unit_quantity,
            occupancy_percentage,
            is_active,
            created_at,
            updated_at,
            property_amenities,
            units_amenities,
            geolocation,
            quality_overall_score,
            stories,
            property_style,
            housing_segment,
            has_affordable_units
        ) VALUES (
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ?, ?, ?, ?,
            ?, ST_SetSRID(ST_MakePoint(?, ?), 4326)::geography,
            ?, ?, ?, ?, ?
        )
        ON CONFLICT (id) DO UPDATE SET
            address = EXCLUDED.address,
            city = EXCLUDED.city,
            county = EXCLUDED.county,
            zip_code = EXCLUDED.zip_code,
            state = EXCLUDED.state,
            latitude = EXCLUDED.latitude,
            longitude = EXCLUDED.longitude,
            square_footage = EXCLUDED.square_footage,
            square_footage_per_unit = EXCLUDED.square_footage_per_unit,
            source_type = EXCLUDED.source_type,
            tract_code = EXCLUDED.tract_code,
            construction_year = EXCLUDED.construction_year,
            renovation_year = EXCLUDED.renovation_year,
            unit_quantity = EXCLUDED.unit_quantity,
            occupancy_percentage = EXCLUDED.occupancy_percentage,
            is_active = EXCLUDED.is_active,
            property_amenities = EXCLUDED.property_amenities,
            units_amenities = EXCLUDED.units_amenities,
            geolocation = EXCLUDED.geolocation,
            quality_overall_score = EXCLUDED.quality_overall_score,
            stories = EXCLUDED.stories,
            property_style = EXCLUDED.property_style,
            housing_segment = EXCLUDED.housing_segment,
            has_affordable_units = EXCLUDED.has_affordable_units,
            updated_at = EXCLUDED.updated_at;
        """.trimIndent()
}

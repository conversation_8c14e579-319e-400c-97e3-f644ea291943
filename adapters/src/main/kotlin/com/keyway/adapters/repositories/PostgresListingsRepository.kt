package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.model.RentListingDBModel
import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.time.LocalDate

class PostgresListingsRepository(
    private val sqlClient: SqlClient,
) : ListingsRepository {
    companion object {
        const val LISTING_INSERT_QUERY_BASE = """
        INSERT INTO rent_listing (
            id, property_id, type, type_id, date_from, date_to, rent, record_source,
            zip_code, msa_code, unit_square_footage, bedrooms, bathrooms, floor_plan, available_in, rent_deposit,
            created_at, updated_at, is_active
        ) VALUES 
    """
    }

    override fun save(rentListing: RentListing) = save(listOf(rentListing))

    override fun save(rentListings: List<RentListing>) {
        if (rentListings.isEmpty()) return

        val valuesSql =
            rentListings.joinToString(", ") {
                "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)"
            }

        val fullQuery = "$LISTING_INSERT_QUERY_BASE $valuesSql"

        sqlClient.update(
            query = fullQuery.trimIndent(),
            preparedStatementHandler = { ps ->
                RentUtils.listingPreparedStatementHandler(rentListings, ps)
            },
        )
    }

    override fun update(rentListings: List<RentListing>) {
        if (rentListings.isEmpty()) return

        val updateQuery =
            """
            UPDATE rent_listing AS rl
            SET 
                property_id = v.property_id,
                type = v.type,
                type_id = v.type_id,
                date_from = v.date_from,
                date_to = v.date_to,
                rent = v.rent,
                record_source = v.record_source,
                zip_code = v.zip_code,
                msa_code = v.msa_code,
                unit_square_footage = v.unit_square_footage,
                bedrooms = v.bedrooms,
                bathrooms = v.bathrooms,
                floor_plan = v.floor_plan,
                available_in = v.available_in,
                rent_deposit = v.rent_deposit,
                created_at = v.created_at,
                updated_at = v.updated_at,
                is_active = v.is_active
            FROM (
                VALUES ${rentListings.joinToString(", ") { "(?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)" }}
            ) AS v (
                id, property_id, type, type_id, date_from, date_to, rent, record_source, zip_code, 
                msa_code, unit_square_footage, bedrooms, bathrooms, floor_plan, available_in, 
                rent_deposit, created_at, updated_at, is_active
            )
            WHERE rl.id = v.id
            """.trimIndent()

        sqlClient.update(
            query = updateQuery,
            preparedStatementHandler = { ps ->
                RentUtils.listingPreparedStatementHandler(rentListings, ps)
            },
        )
    }

    override fun delete(ids: List<String>) {
        val listingsIdsPlaceholders = ids.joinToString(",") { "?" }
        sqlClient.update(
            query = """ DELETE FROM rent_listing WHERE id IN ($listingsIdsPlaceholders)""",
            params = ids,
        )
    }

    override fun findMostRecentListingByPropertyAndUnit(
        propertyId: String,
        type: RentListingType,
        typeId: String,
        from: LocalDate,
    ): RentListing? =
        sqlClient
            .get(
                query =
                    """select *
                       from rent_listing 
                       where property_id = ? and type_id = ? and type = ?
                       and date_from <= ?
                       ${RentUtils.isActiveSql()}
                       order by date_from desc 
                       limit 1
                    """.trimMargin(),
                params = listOf(propertyId, typeId, type.name, from),
                clazz = RentListingDBModel::class.java,
            )?.toUnitRentListing()

    override fun findFutureListingByPropertyAndUnit(
        propertyId: String,
        type: RentListingType,
        typeId: String,
        from: LocalDate,
    ): RentListing? =
        sqlClient
            .get(
                query =
                    """select *
                       from rent_listing 
                       where property_id = ? and type_id = ? and type = ?
                       and date_from > ?
                       ${RentUtils.isActiveSql()}
                       order by date_from desc 
                       limit 1
                    """.trimMargin(),
                params = listOf(propertyId, typeId, type.name, from),
                clazz = RentListingDBModel::class.java,
            )?.toUnitRentListing()

    override suspend fun findLastListingByProperties(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing> =
        withContext(Dispatchers.IO) {
            // TODO: EVALUATE QUERY MIGRATION TO RANK IN TERMS OF PERFORMANCE
            sqlClient
                .getAll(
                    query =
                        """
                        WITH most_recent_aggregation AS (
                            SELECT property_id, type_id, MAX(date_to) AS max_date_to
                            FROM rent_listing
                            WHERE property_id IN (${propertiesListingsQuery.propertyIds.joinToString(",") { "?" }})
                            ${propertiesListingsQuery.dateFrom?.let { " AND date_from >= ?" } ?: ""}
                            ${propertiesListingsQuery.dateTo?.let { " AND date_to <= ?" } ?: ""}
                            ${propertiesListingsQuery.type?.let { " AND type = ?" } ?: ""}
                            ${RentUtils.isActiveSql()}
                            GROUP BY property_id, type_id
                        )
                        SELECT agg.*
                        FROM rent_listing agg
                        JOIN most_recent_aggregation
                        ON agg.property_id = most_recent_aggregation.property_id
                           AND agg.type_id = most_recent_aggregation.type_id
                           AND agg.date_to = most_recent_aggregation.max_date_to
                        WHERE ${RentUtils.isActiveSql(tableName = "agg", withAnd = false)}
                        """.trimIndent(),
                    params =
                        propertiesListingsQuery.propertyIds
                            .toList()
                            .plus(listOfNotNull(propertiesListingsQuery.dateFrom, propertiesListingsQuery.dateTo))
                            .plus(listOfNotNull(propertiesListingsQuery.type?.name)),
                    clazz = RentListingDBModel::class.java,
                ).map {
                    it.toUnitRentListing()
                }
        }

    override suspend fun findListingsForProperties(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing> =
        withContext(Dispatchers.IO) {
            sqlClient
                .getAll(
                    query =
                        """
                        SELECT *
                        FROM rent_listing
                        WHERE property_id IN (${propertiesListingsQuery.propertyIds.joinToString(",") { "?" }})
                        AND date_from <= ?
                        AND date_to >= ?
                        ${RentUtils.isActiveSql()}
                        ${propertiesListingsQuery.typeId?.let { " AND type_id = ?" } ?: ""}
                        ${propertiesListingsQuery.type?.let { " AND type = ?" } ?: ""}
                        ${propertiesListingsQuery.floorPlan?.let { " AND floor_plan = ?" } ?: ""}
                        ${propertiesListingsQuery.bedrooms?.let { " AND bedrooms = ?" } ?: ""}
                        ${propertiesListingsQuery.bathrooms?.let { " AND bathrooms = ?" } ?: ""}
                        """.trimIndent(),
                    params =
                        propertiesListingsQuery.propertyIds.toList().plus(
                            listOfNotNull(
                                propertiesListingsQuery.dateTo,
                                propertiesListingsQuery.dateFrom,
                                propertiesListingsQuery.typeId,
                                propertiesListingsQuery.type?.name,
                                propertiesListingsQuery.floorPlan,
                                propertiesListingsQuery.bedrooms,
                                propertiesListingsQuery.bathrooms,
                            ),
                        ),
                    clazz = RentListingDBModel::class.java,
                ).map {
                    it.toUnitRentListing()
                }
        }

    override fun findListingsForPropertiesAndUnits(propertiesListingsQuery: PropertiesListingsQuery): List<RentListing> {
        val propertyIdsPlaceholders = propertiesListingsQuery.propertyIds.joinToString(",") { "?" }
        val unitsIdsPlaceholders = propertiesListingsQuery.typeIds.joinToString(",") { "?" }

        return sqlClient
            .getAll(
                query =
                    """
                    SELECT *
                    FROM rent_listing
                    WHERE property_id IN ($propertyIdsPlaceholders)
                    AND type_id IN ($unitsIdsPlaceholders)
                    and type = 'UNIT'
                    AND date_from <= ?
                    AND date_to >= ?
                    ${RentUtils.isActiveSql()}
                    """.trimIndent(),
                params =
                    propertiesListingsQuery.propertyIds
                        .toList()
                        .plus(propertiesListingsQuery.typeIds.toList())
                        .plus(
                            listOfNotNull(
                                propertiesListingsQuery.dateTo,
                                propertiesListingsQuery.dateFrom,
                            ),
                        ),
                clazz = RentListingDBModel::class.java,
            ).map {
                it.toUnitRentListing()
            }
    }

    override fun findInactiveListings(limit: Int): List<RentListing> =
        sqlClient
            .getAll(
                query =
                    """
                    SELECT * FROM rent_listing
                    WHERE is_active = false
                    LIMIT $limit
                    """.trimIndent(),
                params = emptyList(),
                clazz = RentListingDBModel::class.java,
            ).map {
                it.toUnitRentListing()
            }

    override fun zipAndMsaFutureAvailabilityByBedroom(
        id: String,
        idType: IdType,
    ): List<BedroomFutureAvailability> =
        sqlClient
            .getAll(
                query =
                    """
                    SELECT ${idType.getSqlColumn()},
                            bedrooms,
                           available_in,
                           SUM(available_units) as available_units
                    FROM bedroom_future_availability
                    WHERE ${idType.getSqlColumn()} = '$id'
                    GROUP BY ${idType.getSqlColumn()},
                            bedrooms,
                           available_in
                    """.trimIndent(),
                params = emptyList(),
            ) {
                Triple(it["bedrooms"].toString().toInt(), LocalDate.parse(it["available_in"].toString()), it["available_units"].toString().toInt())
            }.let { values ->
                values.map { it.first }.distinct().map { bedroom ->
                    BedroomFutureAvailability(
                        bedroom,
                        values.filter { it.first == bedroom }.associate { (bedroom, availableIn, availableUnits) ->
                            Pair(availableIn, availableUnits)
                        },
                    )
                }
            }
}

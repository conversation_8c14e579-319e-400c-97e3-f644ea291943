package com.keyway.adapters.repositories.utils

import com.keyway.adapters.repositories.model.SummarizedView
import com.keyway.core.entities.RentType
import com.keyway.core.utils.DateUtils.toStartOfMonth
import java.time.LocalDate

object ViewsUtils {
    fun generateSummarizedView(
        view: MaterializedViews,
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ): List<SummarizedView> =
        dateFrom.toStartOfMonth().let { start ->
            generateSequence(start) { it.plusMonths(1) }
                .takeWhile { it <= dateTo }
                .map { date -> SummarizedView(date, view) }
                .toList()
        }

    enum class MaterializedViews(
        val prefix: String,
    ) {
        RENT_LISTING("rent_listing_by"),
        EFFECTIVE_RENT("effective_rent_by"),
        SUMMARY("rent_summary_by"),
        BED_SUMMARY("rent_bedroom_summary_by"),
        ;

        fun getViewName(date: LocalDate) = """${this.prefix}_${date.year}_${date.monthValue.toString().padStart(2, '0')}"""

        companion object {
            fun getViewByRentType(rentType: RentType) =
                when (rentType) {
                    RentType.ASKING -> RENT_LISTING
                    RentType.EFFECTIVE -> EFFECTIVE_RENT
                }
        }
    }
}

package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getBigDecimal
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getOptionalInt
import com.keyway.adapters.repositories.utils.PostgresQueryUtils.getString
import com.keyway.adapters.repositories.utils.ViewsUtils
import com.keyway.adapters.repositories.utils.ViewsUtils.generateSummarizedView
import com.keyway.core.dto.GeoMetric
import com.keyway.core.dto.SummarizedMetricDto
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal

class PostgresSummarizedMetricsRepository(
    private val sqlClient: SqlClient,
) : SummarizedMetricsRepository {
    override suspend fun msaAndZipAggregateMetrics(metricsFiltersQuery: MetricsFiltersQuery): List<SummarizedMetricDto> =
        withContext(Dispatchers.IO) {
            val askingDeferred =
                async {
                    sqlClient
                        .getAll(
                            query = buildMetricsQuery(metricsFiltersQuery),
                            params = emptyList(),
                        ) { result -> result }
                        .map {
                            buildDto(it)
                        }
                }

            return@withContext askingDeferred.await()
        }

    private fun buildMetricsQuery(metricsFiltersQuery: MetricsFiltersQuery): String =
        """
         WITH filtered_rent_listings as (
        ${generateSummarizedView(
            view = ViewsUtils.MaterializedViews.SUMMARY.takeIf { metricsFiltersQuery.type == MetricType.BY_ID } ?: ViewsUtils.MaterializedViews.BED_SUMMARY,
            metricsFiltersQuery.dateFrom,
            metricsFiltersQuery.dateTo,
        )
            .joinToString(" UNION ALL ") {
                "SELECT * from ${it.viewName} " +
                    it.getCondition(
                        metricsFiltersQuery.idType,
                        metricsFiltersQuery.ids.first(),
                        metricsFiltersQuery.dateFrom,
                        metricsFiltersQuery.dateTo,
                    )
            }}
            )
            SELECT 
                    ${metricsFiltersQuery.idType.getSqlColumn()} as id,
                    ${ "bedrooms,".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: "" }
                    MAX(total_props) as total_properties,
                    MIN(asking_min) AS asking_min,
                    MAX(asking_max) AS asking_max,
                    ROUND(SUM(asking_sum) / SUM(total_records), 2) AS asking,
                    MIN(effective_min) AS effective_min,
                    MAX(effective_max) AS effective_max,
                    ROUND(SUM(effective_sum) / SUM(total_records), 2) AS effective,
                    MIN(sft_min) AS unit_square_footage_min,
                    MAX(sft_max) AS unit_square_footage_max,
                    ROUND(SUM(sft_sum) / SUM(total_records), 2) AS unit_square_footage,
                    SUM(total_records)::BIGINT AS total_records,
                    ROUND(SUM(day_listing_since_publish) / SUM(total_records), 2) as average_listing_days
                FROM filtered_rent_listings
                GROUP BY ${metricsFiltersQuery.idType.getSqlColumn()}
                ${" , bedrooms".takeIf { metricsFiltersQuery.type == MetricType.BEDROOMS } ?: ""}
        """.trimIndent()

    private fun buildDto(result: Map<String, Any>): SummarizedMetricDto =
        SummarizedMetricDto(
            id = result.getString("id"),
            totalRecords = result.getInt("total_records"),
            averageListingsDays = result.getBigDecimal("average_listing_days") ?: BigDecimal.ZERO,
            askingRent = buildGeoMetric(result, "asking")!!,
            effectiveRent = buildGeoMetric(result, "effective")!!,
            squareFootage = buildGeoMetric(result, "unit_square_footage"),
            bedrooms = result.getOptionalInt("bedrooms"),
            totalProperties = result.getInt("total_properties"),
        )

    private fun buildGeoMetric(
        result: Map<String, Any>,
        prefix: String,
    ): GeoMetric? {
        val min = result.getBigDecimal("${prefix}_min")
        val max = result.getBigDecimal("${prefix}_max")
        val average = result.getBigDecimal(prefix)

        return takeIf { min != null && max != null && average != null }?.let {
            GeoMetric(min = min!!, max = max!!, average = average!!)
        }
    }
}

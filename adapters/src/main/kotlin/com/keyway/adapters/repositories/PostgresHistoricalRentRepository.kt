package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.IdTypeUtils.getSqlColumn
import com.keyway.adapters.repositories.utils.RentUtils
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getUnitConditionJoin
import com.keyway.adapters.repositories.utils.UnitConditionUtils.getWhere
import com.keyway.adapters.repositories.utils.ViewsUtils
import com.keyway.adapters.repositories.utils.ViewsUtils.generateSummarizedView
import com.keyway.core.dto.HistoricalConcessionValue
import com.keyway.core.dto.HistoricalExposureValue
import com.keyway.core.dto.historical.HistoricalDateRange
import com.keyway.core.dto.historical.HistoricalDaysOnMarketDto
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.historical.HistoricalSummarizedRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.utils.DateUtils.isBetween
import com.keyway.core.utils.DateUtils.toEndOfMonth
import com.keyway.core.utils.distributeEvenly
import com.keyway.kommons.db.SqlClient
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import java.time.temporal.TemporalAdjusters
import kotlin.collections.maxBy

class PostgresHistoricalRentRepository(
    private val sqlClient: SqlClient,
) : HistoricalRentRepository {
    private fun LocalDate.toSqlString(): String = "'$this'::DATE"

    private fun getDataSeriesSql(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): String =
        when (periodicity) {
            HistoricalPeriodicity.DAILY -> """
            WITH date_series AS (
                SELECT
                    date_val::DATE AS date_from,
                    date_val::DATE AS date_to
                FROM generate_series(
                    ${dateFrom.toSqlString()},
                    ${dateTo.toSqlString()},
                    INTERVAL '1 DAY'
                ) AS date_val
            )
        """

            HistoricalPeriodicity.WEEKLY -> """
            WITH date_series AS (
                    SELECT
                        date_val::DATE AS date_from,
                        (date_val::DATE + INTERVAL '6 days')::date AS date_to
                    FROM generate_series(
                        DATE_TRUNC('WEEK', ${dateFrom.toSqlString()}),
                        ${dateTo.toSqlString()},
                        INTERVAL '1 WEEK'
                    ) AS date_val
                )
        """

            HistoricalPeriodicity.MONTHLY -> """
            WITH date_series AS (
                SELECT
                    date_val::DATE AS date_from,
                    (date_val::DATE + INTERVAL '1 month' - INTERVAL '1 day')::date AS date_to
                FROM generate_series(
                    DATE_TRUNC('MONTH',${dateFrom.toSqlString()}),
                    ${dateTo.toSqlString()},
                    INTERVAL '1 MONTH'
                ) AS date_val
            )
        """
        }

    private fun getDataSeries(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): List<Pair<LocalDate, LocalDate>> =
        when (periodicity) {
            HistoricalPeriodicity.DAILY -> {
                generateSequence(dateFrom) { date ->
                    date.plusDays(1).takeIf { it <= dateTo }
                }.map { date -> date to date }.toList()
            }

            HistoricalPeriodicity.WEEKLY -> {
                val startOfWeek = dateFrom.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY))
                generateSequence(startOfWeek) { date ->
                    date.plusWeeks(1).takeIf { it <= dateTo }
                }.map { weekStart ->
                    weekStart to weekStart.plusDays(6)
                }.toList()
            }

            HistoricalPeriodicity.MONTHLY -> {
                val startOfMonth = dateFrom.with(TemporalAdjusters.firstDayOfMonth())
                generateSequence(startOfMonth) { date ->
                    date.plusMonths(1).takeIf { it <= dateTo }
                }.map { monthStart ->
                    monthStart to monthStart.with(TemporalAdjusters.lastDayOfMonth())
                }.toList()
            }
        }

    override suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            when (idType) {
                IdType.ZIP_CODE, IdType.MSA ->
                    getGeoData(
                        ids,
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                    )
                else ->
                    getPropertyData(
                        ids.joinToString(",") { "'$it'" },
                        idType,
                        dateFrom,
                        dateTo,
                        periodicity,
                        rentType,
                        bedrooms,
                        bathrooms,
                        unitCondition,
                    )
            }
        }

    override suspend fun getExposureByZipOrMsa(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): List<HistoricalExposureValue> {
        val views = generateSummarizedView(ViewsUtils.MaterializedViews.SUMMARY, dateFrom, dateTo)
        val query =
            """ 
            ${getDataSeriesSql(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                ${views.joinToString(" UNION ALL ") {
                """
                        SELECT date_of_record, exposure
                            FROM ${it.viewName} ${
                    it.getCondition(
                        idType,
                        id,
                        dateFrom,
                        dateTo,
                        null,
                    )
                } """
            }} 
            )
            SELECT 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                AVG(exposure) as avg_exposure_rate
            FROM filtered_data join date_series 
                ON filtered_data.date_of_record BETWEEN date_series.date_from AND date_series.date_to
            GROUP BY date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient
            .getAll(
                query = query,
                params = emptyList(),
                clazz = HistoricalExposureValue::class.java,
            ).let { response ->
                response.getRangeToCopy(periodicity, dateTo)?.let { (dateFrom, dateTo) ->
                    response + response.maxBy { dateTo }.copy(dateFrom = dateFrom, dateTo = dateTo)
                } ?: response
            }
    }

    override suspend fun getConcessionByZipOrMsa(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): List<HistoricalConcessionValue> {
        val query =
            generateSummarizedView(ViewsUtils.MaterializedViews.SUMMARY, dateFrom, dateTo).let { views ->
                """ 
                ${getDataSeriesSql(dateFrom, dateTo, periodicity)},
                    filtered_data AS (
                    ${
                    views.joinToString(" UNION ALL ") {
                        """
                        SELECT date_of_record, 
                            concession_rate,
                            concession_amount,
                            props_with_concession,
                            total_props
                            FROM ${it.viewName} ${
                            it.getCondition(
                                idType,
                                id,
                                dateFrom,
                                dateTo,
                                null,
                            )
                        }  AND concession_rate is not null """
                    }
                } 
                )
                SELECT 
                    date_series.date_from as date_from,
                    date_series.date_to as date_to,
                    AVG(concession_rate) as avg_concession_rate,
                    AVG(concession_amount) as avg_concession_value,
                    SUM(props_with_concession) as total_properties_with_concession,
                    SUM(total_props) as total_properties
                FROM filtered_data join date_series 
                    ON filtered_data.date_of_record BETWEEN date_series.date_from AND date_series.date_to
                GROUP BY date_series.date_from, date_series.date_to
                """.trimIndent()
            }

        return sqlClient
            .getAll(
                query = query,
                params = emptyList(),
                clazz = HistoricalConcessionValue::class.java,
            ).let { response ->
                response.getRangeToCopy(periodicity, dateTo)?.let { (dateFrom, dateTo) ->
                    response + response.maxBy { dateTo }.copy(dateFrom = dateFrom, dateTo = dateTo)
                } ?: response
            }
    }

    override suspend fun getDaysOnMarket(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        bedrooms: Int?,
    ): List<HistoricalDaysOnMarketDto> =
        getPropertyDaysOnMarket(
            sqlId = id,
            idType = idType,
            dateFrom = dateFrom,
            dateTo = dateTo,
            periodicity = periodicity,
            bedrooms = bedrooms,
        )

    private fun HistoricalPeriodicity.dateOfRecordField() =
        when (this) {
            HistoricalPeriodicity.DAILY -> "date_of_record"
            HistoricalPeriodicity.WEEKLY -> "DATE_TRUNC('WEEK', date_of_record)::DATE"
            HistoricalPeriodicity.MONTHLY -> "DATE_TRUNC('MONTH', date_of_record)::DATE"
        }

    private suspend fun getGeoData(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
    ): List<HistoricalRentOutput> =
        withContext(Dispatchers.IO) {
            val matView = ViewsUtils.MaterializedViews.SUMMARY.takeIf { bedrooms == null } ?: ViewsUtils.MaterializedViews.BED_SUMMARY
            val views = generateSummarizedView(matView, dateFrom, dateTo).distributeEvenly(3)
            val dateField = periodicity.dateOfRecordField()
            val rentField =
                when (rentType) {
                    RentType.ASKING -> "asking_sum"
                    RentType.EFFECTIVE -> "effective_sum"
                }
            val deferredResults =
                views.map { queries ->
                    async {
                        sqlClient
                            .getAll(
                                query =
                                    queries
                                        .joinToString(" UNION ALL ") {
                                            """ SELECT 
                            ${idType.getSqlColumn()} as id, 
                            $dateField as date_of_record,
                            SUM(total_records) as total_records,
                            SUM(sft_sum) as total_square_footage,
                            SUM($rentField) as total_rent,                
                            SUM(CASE WHEN sft_sum IS NOT NULL THEN $rentField ELSE 0 END) as total_rent_with_square_footage,
                            SUM(CASE WHEN sft_sum IS NOT NULL THEN 1 ELSE 0 END) as total_records_with_square_footage 
                           FROM ${it.viewName} ${it.getCondition(
                                                idType,
                                                ids,
                                                dateFrom,
                                                dateTo,
                                                bedrooms,
                                            )} 
                        GROUP BY ${idType.getSqlColumn()}, $dateField """
                                        }.trimIndent(),
                                params = emptyList(),
                                clazz = HistoricalSummarizedRentOutput::class.java,
                            )
                    }
                }

            val dataSeries = getDataSeries(dateFrom, dateTo, periodicity)
            val results =
                deferredResults
                    .awaitAll()
                    .flatten()
                    .mapNotNull { result ->
                        dataSeries
                            .find { (dateFrom, dateTo) ->
                                result.dateOfRecord.isBetween(dateFrom, dateTo)
                            }?.first
                            ?.let { dateKey -> dateKey to result }
                    }.groupBy({ it.first }, { it.second })
            dataSeries
                .mapNotNull { (dateFrom, dateTo) ->
                    results[dateFrom]?.toHistoricalRentOutput(dateFrom, dateTo)
                }.let { response ->
                    response.getRangeToCopy(periodicity, dateTo)?.let { (dateFrom, dateTo) ->
                        response + response.maxBy { dateTo }.copy(dateFrom = dateFrom, dateTo = dateTo)
                    } ?: response
                }
        }

    private fun List<HistoricalSummarizedRentOutput>.toHistoricalRentOutput(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ) = HistoricalRentOutput(
        id = this.first().id,
        dateFrom = dateFrom,
        dateTo = dateTo,
        rentMedian = null,
        totalRecords = this.sumOf { it.totalRecords },
        totalRent = this.sumOf { it.totalRent },
        minRent = this.minOf { it.totalRent },
        maxRent = this.maxOf { it.totalRent },
        totalSquareFootage = this.mapNotNull { it.totalSquareFootage }.takeUnless { it.isEmpty() }?.sumOf { it },
        totalRentWithSquareFootage = this.mapNotNull { it.totalRentWithSquareFootage }.takeUnless { it.isEmpty() }?.sumOf { it },
        totalRecordsWithSquareFootage = this.sumOf { it.totalRecordsWithSquareFootage },
    )

    private fun List<HistoricalDateRange>.getRangeToCopy(
        periodicity: HistoricalPeriodicity,
        dateTo: LocalDate,
    ): Pair<LocalDate, LocalDate>? =
        this
            .takeIf { it.isNotEmpty() }
            ?.maxBy { it.dateTo }
            ?.let { lastRecord ->
                val tempUnit =
                    when (periodicity) {
                        HistoricalPeriodicity.DAILY -> ChronoUnit.DAYS
                        HistoricalPeriodicity.WEEKLY -> ChronoUnit.WEEKS
                        HistoricalPeriodicity.MONTHLY -> ChronoUnit.MONTHS
                    }
                lastRecord
                    .takeUnless { dateTo.isBetween(lastRecord.dateFrom, lastRecord.dateTo) }
                    ?.let {
                        Pair(
                            lastRecord.dateFrom.plus(1, tempUnit),
                            lastRecord.dateTo.plus(1, tempUnit).let { newDateTo ->
                                newDateTo.takeUnless { periodicity == HistoricalPeriodicity.MONTHLY }
                                    ?: newDateTo.toEndOfMonth()
                            },
                        )
                    }
            }

    private fun getPropertyDaysOnMarket(
        sqlId: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        bedrooms: Int?,
    ): List<HistoricalDaysOnMarketDto> {
        val query =
            """ 
                ${getDataSeriesSql(dateFrom, dateTo, periodicity)},
                 filtered_data AS (
                 ${buildFilteredQuery(idType, sqlId, dateFrom, dateTo, bedrooms)}
                 )
                 SELECT
                ds.date_to AS date_to,
                ds.date_from AS date_from,
                ROUND(AVG(LEAST(ds.date_to, fl.date_to) - fl.date_from + 1), 2) AS listing_days
            FROM date_series ds
            CROSS JOIN filtered_data fl
            WHERE fl.date_from <= ds.date_to
                AND fl.date_to >= ds.date_from
            GROUP BY  ds.date_to, ds.date_from
            ORDER BY ds.date_to ASC 
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            clazz = HistoricalDaysOnMarketDto::class.java,
        )
    }

    private fun getPropertyData(
        sqlIds: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput> {
        val query =
            """
                ${getDataSeriesSql(dateFrom, dateTo, periodicity)},
                filtered_data AS (
                    ${buildQuery(
                when (rentType) {
                    RentType.ASKING -> "rent_listing"
                    RentType.EFFECTIVE -> "effective_rent"
                },
                idType,
                sqlIds,
                dateTo,
                dateFrom,
                bedrooms,
                bathrooms,
                unitCondition = unitCondition,
            )}   
                )
                 SELECT 
                id as id, 
                date_series.date_from as date_from,
                date_series.date_to as date_to,
                count(*) as total_records,
                SUM(unit_square_footage) as total_square_footage,
                SUM(CASE WHEN unit_square_footage IS NOT NULL THEN rent ELSE 0 END) as total_rent_with_square_footage,
                SUM(CASE WHEN unit_square_footage IS NOT NULL THEN 1 ELSE 0 END) as total_records_with_square_footage,
                SUM(rent) as total_rent,
                ROUND(MIN(rent), 2) as min_rent,
                ROUND(MAX(rent), 2) as max_rent,
                ROUND((percentile_cont(0.5) WITHIN GROUP (ORDER BY rent))::numeric, 2) AS rent_median
            FROM filtered_data join date_series 
                ON filtered_data.date_from <= date_series.date_to 
                    AND filtered_data.date_to >= date_series.date_from
            GROUP BY id, date_series.date_from, date_series.date_to
            """.trimIndent()

        return sqlClient.getAll(
            query = query,
            params = emptyList(),
            clazz = HistoricalRentOutput::class.java,
        )
    }

    private fun buildFilteredQuery(
        idType: IdType,
        sqlId: String,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        bedrooms: Int?,
    ) = """
        SELECT
           ${idType.getSqlColumn()} ,
            date_from,
            date_to
        FROM rent_listing
        WHERE ${idType.getSqlColumn()} = '$sqlId'
            AND is_active = true
             ${bedrooms?.let { " AND rent_listing.bedrooms = $it " } ?: ""}
             AND date_from <= ${dateTo.toSqlString()} AND date_to >= ${dateFrom.toSqlString()}
        """.trimIndent()

    private fun buildQuery(
        tableName: String,
        idType: IdType,
        sqlIds: String,
        dateTo: LocalDate,
        dateFrom: LocalDate,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ) = """
        SELECT 
        rent_data.${idType.getSqlColumn()} as id,
        rent_data.${"id".takeIf { tableName == "rent_listing" } ?: "rent_listing_id"} as listing_id,
        AVG(rent_data.unit_square_footage) as unit_square_footage, 
        AVG(rent_data.rent) as rent,
        MIN(rent_data.date_from) as date_from,
        MAX(rent_data.date_to) as date_to
           FROM $tableName rent_data
           ${unitCondition?.getUnitConditionJoin(rentAlias = "rent_data") ?: ""}
           WHERE rent_data.${idType.getSqlColumn()} IN ($sqlIds)
             AND date_from <= ${dateTo.toSqlString()}
             AND date_to >= ${dateFrom.toSqlString()}
             ${RentUtils.isActiveSql(tableName = "rent_data")}
             ${bedrooms?.let { " AND rent_data.bedrooms = $it " } ?: ""}
             ${bathrooms?.let { " AND rent_data.bathrooms = $it" } ?: ""}
             ${unitCondition?.getWhere() ?: ""}
           GROUP BY 1,2
        """.trimIndent()
}

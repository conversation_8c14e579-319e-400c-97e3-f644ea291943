package com.keyway.adapters.repositories

import com.keyway.adapters.repositories.utils.ViewsUtils
import com.keyway.adapters.repositories.utils.ViewsUtils.generateSummarizedView
import com.keyway.core.entities.property.PropertyLastSeenData
import com.keyway.core.ports.repositories.PropertyMetricsRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.db.SqlClient

class PostgresPropertyMetricRepository(
    private val sqlClient: SqlClient,
) : PropertyMetricsRepository {
    override fun getLastSeenByPropertyInLast(
        offset: Int,
        limit: Int,
        days: Int,
        propertyIds: Set<String>,
        zipCodes: Set<String>,
    ): List<PropertyLastSeenData> =
        generateSummarizedView(
            ViewsUtils.MaterializedViews.RENT_LISTING,
            DateUtils.getDefaultDateTo().minusDays(days.toLong()),
            DateUtils.getDefaultDateTo(),
        ).let { views ->
            """
                     WITH filtered_data AS (
                        ${
                views.joinToString(" UNION ALL ") { view ->
                    """
                        SELECT property_id, MAX(date_of_record) as last_seen 
                            FROM ${view.viewName}
                            WHERE 1 = 1
                            ${
                        propertyIds.writeIfNotEmpty(
                            " AND property_id IN (${propertyIds.joinToString(",") { "'$it'" }}) ",
                        )
                    }
                            ${
                        zipCodes.writeIfNotEmpty(
                            " AND zip_code IN (${zipCodes.joinToString(",") { "'$it'" }}) ",
                        )
                    }
                          GROUP BY property_id
                        """
                }
            } 
                    )         
                    SELECT property_id, MAX(last_seen) as last_seen 
                    FROM filtered_data
                    GROUP BY property_id
                    ORDER BY property_id
                    OFFSET ?
                    LIMIT ?
            """.trimIndent()
        }.let {
            sqlClient.getAll(
                query = it,
                params = listOf(offset, limit),
                clazz = PropertyLastSeenData::class.java,
            )
        }

    private fun Set<String>.writeIfNotEmpty(condition: String) = condition.takeIf { this.isNotEmpty() } ?: ""
}

package com.keyway.adapters.executor

import com.keyway.adapters.converters.exception.ExceptionsConverter
import com.keyway.core.usecases.UseCase
import com.keyway.core.usecases.UseCaseAsync

interface UseCaseExecutor {
    operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: UseCase<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ): OutputDto

    operator fun <InputDto, Input> invoke(
        useCase: UseCase<Input, Unit>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, inputDto, inputConverter, {}, exceptionConverter)

    operator fun invoke(
        useCase: UseCase<Unit, Unit>,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, Unit, { }, exceptionConverter)

    operator fun <OutputDto, Output> invoke(
        useCase: UseCase<Unit, Output>,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, Unit, { }, outputConverter, exceptionConverter)

    suspend operator fun <InputDto, OutputDto, Input, Output> invoke(
        useCase: UseCaseAsync<Input, Output>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ): OutputDto

    suspend operator fun <InputDto, Input> invoke(
        useCase: UseCaseAsync<Input, Unit>,
        inputDto: InputDto,
        inputConverter: (InputDto) -> Input,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, inputDto, inputConverter, {}, exceptionConverter)

    suspend operator fun invoke(
        useCase: UseCaseAsync<Unit, Unit>,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, Unit, { }, exceptionConverter)

    suspend operator fun <OutputDto, Output> invoke(
        useCase: UseCaseAsync<Unit, Output>,
        outputConverter: (Output) -> OutputDto,
        exceptionConverter: (Throwable) -> Exception = ExceptionsConverter::invoke,
    ) = invoke(useCase, Unit, { }, outputConverter, exceptionConverter)
}

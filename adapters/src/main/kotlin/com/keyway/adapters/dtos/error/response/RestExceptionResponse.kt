package com.keyway.adapters.dtos.error.response

data class RestExceptionResponse(
    val message: String,
    val httpStatusCode: Int,
    val statusCode: Int,
    val errorCode: String,
) {
    override fun toString(): String =
        "message: ${this.message}, " +
            "http_status_code: ${this.httpStatusCode}, " +
            "status_code: ${this.statusCode}, " +
            "error_code: ${this.errorCode}"
}

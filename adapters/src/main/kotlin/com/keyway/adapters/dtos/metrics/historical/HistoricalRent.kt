package com.keyway.adapters.dtos.metrics.historical

import com.keyway.adapters.dtos.metrics.MetricDetail
import com.keyway.core.entities.RentType
import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalRentSummary(
    val propertyId: String,
    val historicalRents: List<HistoricalRent>,
    override val rentType: RentType,
    override val rentChange: BigDecimal,
) : HistoricalSummary

data class HistoricalRent(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val averageRent: BigDecimal,
    val averageRentPSF: BigDecimal?,
    val medianRent: BigDecimal,
    val medianRentPSF: BigDecimal?,
    val rent: MetricDetail,
    val rentSPF: MetricDetail?,
)

sealed interface HistoricalSummary {
    val rentType: RentType
    val rentChange: BigDecimal
}

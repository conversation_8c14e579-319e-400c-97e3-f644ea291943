package com.keyway.adapters.dtos.metrics.historical

import com.keyway.core.entities.RentType
import java.math.BigDecimal
import java.time.LocalDate

data class ZipHistoricalRentSummary(
    val zipCode: String,
    override val rentType: RentType,
    val historicalRents: List<GeoHistoricalRent>,
    override val rentChange: BigDecimal,
) : HistoricalSummary

data class MSAHistoricalRentSummary(
    val msaCode: String,
    override val rentType: RentType,
    val historicalRents: List<GeoHistoricalRent>,
    override val rentChange: BigDecimal,
) : HistoricalSummary

data class GeoHistoricalRent(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val averageRent: BigDecimal,
    val averageRentPSF: BigDecimal?,
)

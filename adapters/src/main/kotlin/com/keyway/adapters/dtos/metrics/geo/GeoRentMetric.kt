package com.keyway.adapters.dtos.metrics.geo

import com.keyway.adapters.dtos.metrics.GeoMetricDetail
import java.math.BigDecimal
import java.time.LocalDate

data class MSARentSummary(
    val msaCode: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val metrics: List<GeoRentMetric>,
)

data class ZipRentSummary(
    val zipCode: String,
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val metrics: List<GeoRentMetric>,
)

data class GeoRentMetric(
    val squareFootage: GeoMetricDetail?,
    val askingRent: GeoMetricDetail,
    val askingRentPSF: GeoMetricDetail?,
    val effectiveRent: GeoMetricDetail,
    val effectiveRentPSF: GeoMetricDetail?,
    val recordsQuantity: Int,
    val averageListingDays: BigDecimal,
    val totalProperties: Int,
)

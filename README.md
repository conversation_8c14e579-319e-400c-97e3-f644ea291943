## Requires
* Java 17
* Kotlin 1.9.X
* Gradle 8.X

## Gradle Setup (Build)
For <PERSON>rad<PERSON> to be able to download our packages, you will need to setup two environment variables:
- GH_USERNAME: Your github username
- GH_TOKEN: Your github token

## Project Structure
The project has the following modules structure:

- application: acts as the delivery lawyer, only responsible to expose the app (in this case) as a ktor REST API, including the configuration for the netty server and the endpoint routing. 
- adapters: connects the usecases with the external world, it contains the repository implementations, http clients, controllers and response mappers.
- usescases: every use case receives a request and returns a response, it contains the businness logic of our domain.
- core: only contains the domain entities

## Architecture diagram
![architecture](statics/clean_arch.png)

## Rent tables updates vs materialized views refresh
![architecture](statics/rent-tables-updates.png)




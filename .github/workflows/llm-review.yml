name: Review Bot

on:
  pull_request:
    types: [opened, reopened, synchronize, ready_for_review]
    branches:
      - develop
  issue_comment:
    types: [created, edited]


jobs:
  pr_agent_job:
    if: ${{ github.event.sender.type != 'Bo<PERSON>' }}
    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write
      contents: write
    name: LLM Review
    steps:
      - name: AI-Powered PR Review
        uses: unlockre/github-resources/actions/llm-review@main
        with:
          openai_key: ${{ secrets.OPENAI_KEY }}
          github_token: ${{ secrets.GH_TOKEN }}
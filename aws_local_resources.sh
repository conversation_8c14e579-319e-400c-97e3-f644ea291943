#!/bin/bash

# Array of queue names
declare -a QUEUES=(
    "dev-rent-api-grouped_rent_data"
    "dev-rent-api-unit_rent_data"
    "dev-rent-api-property_concessions"
    "dev-rent-api-property_concessions_v2"
    "dev-rent-background_rent_tasks"
    "dev-rent-property_units_data"
    "dev-rent-property_data"
)

echo "Starting SQS queue creation..."
echo "----------------------------------------"

# Function to create queue and handle errors
create_queue() {
    local queue_name="$1"

    echo "Creating queue: $queue_name..."

    if awslocal sqs create-queue --queue-name "$queue_name" 2>/dev/null; then
        echo -e "${GREEN}✓ Successfully created queue: $queue_name${NC}"
    else
        echo -e "${RED}✗ Failed to create queue: $queue_name${NC}"
        return 1
    fi
}

for queue in "${QUEUES[@]}"; do
    create_queue "$queue"
done


awslocal sqs list-queues

awslocal s3api create-bucket --bucket sqs-oversize-messages

awslocal s3api list-buckets

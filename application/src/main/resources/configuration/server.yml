service_name: "rent-api"

save_grouped_rent_sqs_delay: "${DEFAULT_SQS_RENT_GROUP_DELAY}"
enable_sqs_consumers: "${ENABLE_SQS_CONSUMERS}"
aws_database_service_name: "${AWS_DATABASE_SERVICE_NAME}"

system:
  api_url: "${API_URL}"
  http_port: "8080"
  timeout: 10000

datadog_config:
  step_in_seconds: 20
  api_key: "${DATADOG_API_KEY}"

data_base_config:
  jdbc_url: "${POSTGRES_DB_URL}"
  username: "${POSTGRES_DB_USER}"
  password: "${POSTGRES_DB_PASSWORD}"
  driver_class_name: "org.postgresql.Driver"
  minimum_idle: "${POSTGRES_DB_MINIMUM_IDLE}"
  maximum_pool_size: "${POSTGRES_DB_MAX_POOL_SIZE}"
  connection_timeout: "${POSTGRES_DB_CONNECTION_TIMEOUT}"
  idle_timeout: "${POSTGRES_DB_IDLE_TIMEOUT}"
  autocommit: true

aws_config:
  region: "${AWS_REGION}"
  account_id: "${AWS_ACCOUNT}"
  access_key: "${AWS_ACCESS_KEY}"
  secret_key: "${AWS_SECRET_KEY}"

publish_queue: "${AWS_SQS_LAST_SEEN_QUEUE}"

sqs_config:
  queue_configs:
    - key: property_concessions_v2
      name: "${AWS_SQS_PROPERTY_CONCESSIONS_V2}"
      workers: "${AWS_SQS_PROPERTY_CONCESSIONS_WORKERS_V2}"
      wait_time_seconds: 1
      max_number_of_messages: "${AWS_SQS_PROPERTY_CONCESSIONS_MAX_MESSAGES_V2}"
      visibility_timeout: 20
    - key: grouped_unit_rent_data
      name: "${AWS_SQS_UNIT_RENT_DATA}"
      workers: "${AWS_SQS_UNIT_RENT_DATA_WORKERS}"
      wait_time_seconds: 1
      max_number_of_messages: "${AWS_SQS_UNIT_RENT_DATA_MAX_MESSAGES}"
      visibility_timeout: 20
    - key: background_rent_tasks
      name: "${AWS_SQS_BACKGROUND_TASKS}"
      workers: "${AWS_SQS_BACKGROUND_TASKS_WORKERS}"
      wait_time_seconds: 1
      max_number_of_messages: "${AWS_SQS_BACKGROUND_TASKS_MAX_MESSAGES}"
      visibility_timeout: 20
    - key: property_units_data
      name: "${AWS_SQS_PROPERTY_UNIT_DATA}"
      workers: "${AWS_SQS_PROPERTY_UNIT_DATA_WORKERS}"
      wait_time_seconds: 1
      max_number_of_messages: "${AWS_SQS_PROPERTY_UNIT_DATA_MAX_MESSAGES}"
      visibility_timeout: 20
    - key: property_data
      name: "${AWS_SQS_PROPERTY_DATA}"
      workers: "${AWS_SQS_PROPERTY_DATA_WORKERS}"
      wait_time_seconds: 1
      max_number_of_messages: "${AWS_SQS_PROPERTY_DATA_MAX_MESSAGES}"
      visibility_timeout: 20
drop procedure if exists create_monthly_rent_views();

create or replace procedure create_monthly_rent_views(
    start_date date default '2022-01-01'::date,
    end_date date default CURRENT_DATE + INTERVAL '1 month'
)
    language plpgsql
as
$$

DECLARE

    current_date_2               DATE;
    rent_listing_view_name       TEXT;
    effective_rent_view_name     TEXT;
    summarized_view_name         TEXT;
    bedroom_summarized_view_name TEXT;
BEGIN

    current_date_2 := start_date;

    WHILE current_date_2 <= end_date
        LOOP
            rent_listing_view_name := format('rent_listing_by_%s_%s',
                                             TO_CHAR(current_date_2, 'YYYY'),
                                             TO_CHAR(current_date_2, 'MM'));

            -- Check if the view already exists
            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = rent_listing_view_name) THEN

                CALL create_monthly_rent_listing_view(rent_listing_view_name, current_date_2);
                CALL add_index_to_created_view(rent_listing_view_name);

                -- Log and execute
                RAISE NOTICE 'Creating materialized view: %', rent_listing_view_name;

            ELSE
                RAISE NOTICE 'View % already exists. Skipping.', rent_listing_view_name;

            END IF;

            effective_rent_view_name := format('effective_rent_by_%s_%s',
                                               TO_CHAR(current_date_2, 'YYYY'),
                                               TO_CHAR(current_date_2, 'MM'));
            -- Check if the view already exists
            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = effective_rent_view_name) THEN

                CALL create_monthly_effective_rent_view(effective_rent_view_name, current_date_2);
                CALL add_index_to_created_view(effective_rent_view_name);

                -- Log and execute
                RAISE NOTICE 'Creating materialized view: %', effective_rent_view_name;

            ELSE

                RAISE NOTICE 'View % already exists. Skipping.', effective_rent_view_name;

            END IF;

            summarized_view_name := format('rent_summary_by_%s_%s',
                                           TO_CHAR(current_date_2, 'YYYY'),
                                           TO_CHAR(current_date_2, 'MM'));
            bedroom_summarized_view_name := format('rent_bedroom_summary_by_%s_%s',
                                                   TO_CHAR(current_date_2, 'YYYY'),
                                                   TO_CHAR(current_date_2, 'MM'));
            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = rent_listing_view_name
                             and ispopulated = true) THEN
                CALL refresh_materialized_views(rent_listing_view_name);
            END IF;

            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = effective_rent_view_name
                             and ispopulated = true) THEN
                CALL refresh_materialized_views(effective_rent_view_name);
            END IF;

            CALL create_monthly_summarized_view(rent_listing_view_name, effective_rent_view_name, summarized_view_name);
            CALL create_monthly_bed_summarized_view(rent_listing_view_name, effective_rent_view_name,
                                                    bedroom_summarized_view_name);

            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = summarized_view_name
                             and ispopulated = true) THEN
                CALL refresh_materialized_views(summarized_view_name);
            END IF;

            IF NOT EXISTS (SELECT 1
                           FROM pg_matviews
                           WHERE matviewname = bedroom_summarized_view_name
                             and ispopulated = true) THEN
                CALL refresh_materialized_views(bedroom_summarized_view_name);
            END IF;

            -- Move to the next month
            current_date_2 := current_date_2 + INTERVAL '1 month';
        END LOOP;

END
$$;
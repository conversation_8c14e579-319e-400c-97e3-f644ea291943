create materialized view if not exists bedroom_future_availability as
SELECT zip_code,
       msa_code,
       bedrooms,
       available_in,
       count(DISTINCT type_id) as available_units
FROM rent_listing
WHERE date_from >= (CURRENT_DATE - interval '3 months')
  and available_in is not null
  and available_in > CURRENT_DATE
  and type = 'UNIT'
group by zip_code,
         msa_code,
         bedrooms,
         available_in;

create index if not exists idx_zip_code_effective_bedroom_future_availability
    on bedroom_future_availability (zip_code);

create index if not exists idx_msa_code_effective_bedroom_future_availability
    on bedroom_future_availability (msa_code);

CREATE UNIQUE INDEX  IF NOT EXISTS unique_idx_effective_bedroom_future_availability
                        ON bedroom_future_availability (msa_code, zip_code, bedrooms, available_in )

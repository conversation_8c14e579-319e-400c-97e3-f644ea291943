create or replace procedure add_index_to_created_view(IN view_name text)
    language plpgsql
as
$$
DECLARE

    zip_code_index_name TEXT :='';
    msa_code_index_name TEXT :='';
    unique_index_name TEXT :='';

BEGIN

        RAISE NOTICE 'Creating indexes in view: %', view_name;

        zip_code_index_name := 'idx_zip_code_'|| view_name;
        msa_code_index_name := 'idx_msa_code_'|| view_name;
        unique_index_name := 'unique_idx_'|| view_name;


        EXECUTE '
                  CREATE INDEX IF NOT EXISTS ' || quote_ident(zip_code_index_name) ||
                    ' ON ' || quote_ident(view_name) || ' (zip_code)
                ';
                RAISE NOTICE 'Index % created.', zip_code_index_name;

        EXECUTE '
                CREATE INDEX IF NOT EXISTS ' || quote_ident(msa_code_index_name) ||
                ' ON ' || quote_ident(view_name) || ' (msa_code)
            ';
                RAISE NOTICE 'Index % created.', msa_code_index_name;

         EXECUTE '
                   CREATE UNIQUE INDEX  IF NOT EXISTS ' || quote_ident(unique_index_name) ||
                        ' ON ' || quote_ident(view_name) || ' (msa_code, zip_code, property_id, bedrooms, date_of_record )
                        ';
                   RAISE NOTICE 'Index % created.', unique_index_name;

END $$;

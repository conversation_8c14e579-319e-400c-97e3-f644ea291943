CREATE OR REPLACE PROCEDURE create_monthly_summarized_view(IN listing_view_name text,
                                                               IN effective_view_name text,
                                                               IN summarized_view_name text)
AS
$$
DECLARE
    sql TEXT := '';
BEGIN
    -- Skip creation if view already exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews WHERE matviewname = summarized_view_name
    ) THEN
        RAISE NOTICE 'View % already exists. Skipping.', summarized_view_name;
        RETURN;
    END IF;

    -- Build dynamic SQL
    sql := 'CREATE MATERIALIZED VIEW IF NOT EXISTS ' || quote_ident(summarized_view_name) || ' AS ' ||
          ' with prop_data as (select rl.property_id,
                          rl.zip_code,
                          rl.msa_code,
                          rl.date_of_record,
                          sum(rl.total_records)             as total_records,
                          --asking
                          min(rl.min_rent)                  as asking_min,
                          sum(rl.rent_sum)                  as asking_sum,
                          max(rl.max_rent)                  as asking_max,
                          --effective
                          min(er.min_rent)                  as effective_min,
                          sum(er.rent_sum)                  as effective_sum,
                          max(er.max_rent)                  as effective_max,
                          -- square_feet
                          min(rl.min_sft)                   as sft_min,
                          sum(rl.sft_sum)                   as sft_sum,
                          max(rl.max_sft)                   as sft_max,
                          -- listing_days
                          sum(rl.day_listing_since_publish) as day_listing_since_publish,
                          sum(rl.total_day_listing)         as total_day_listing
                   from '|| quote_ident(listing_view_name) ||' rl
                            inner join '|| quote_ident(effective_view_name) ||' er
                                       on rl.property_id = er.property_id
                                           and rl.bedrooms = er.bedrooms
                                           and rl.date_of_record = er.date_of_record
                   group by rl.property_id,
                            rl.zip_code,
                            rl.msa_code,
                            rl.date_of_record)
select msa_code,
       prop_data.zip_code,
       date_of_record,
       sum(total_records)                                                           as total_records,
       --asking
       min(asking_min)                                                              as asking_min,
       sum(asking_sum)                                                              as asking_sum,
       max(asking_max)                                                              as asking_max,
       --effective
       min(effective_min)                                                           as effective_min,
       sum(effective_sum)                                                           as effective_sum,
       max(effective_max)                                                           as effective_max,
       -- square_feet
       min(sft_min)                                                                 as sft_min,
       sum(sft_sum)                                                                 as sft_sum,
       max(sft_max)                                                                 as sft_max,
       -- listing_days
       sum(day_listing_since_publish)                                               as day_listing_since_publish,
       sum(total_day_listing)                                                       as total_day_listing,
       -- metrics
       avg(case
               when mf.unit_quantity > 0
                   then total_records / unit_quantity end)
                                                                                    as exposure,
       avg((asking_sum::numeric - effective_sum::numeric) / asking_sum::numeric)    as concession_rate,
       avg((asking_sum::numeric - effective_sum::numeric) / total_records::numeric) as concession_amount,
       sum(case when asking_sum - effective_sum > 0 then 1 else 0 end)              as props_with_concession,
       count(distinct property_id)                                                  as total_props
from prop_data
         inner join multifamily_properties mf
                    on mf.id = prop_data.property_id
group by msa_code, prop_data.zip_code, date_of_record WITH NO DATA;';

    -- Execute SQL
    RAISE NOTICE 'Creating materialized view: %', summarized_view_name;
    EXECUTE sql;


    EXECUTE '
                  CREATE INDEX IF NOT EXISTS idx_zip_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (zip_code)
                ';
    RAISE NOTICE 'Index % created.', 'idx_zip_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                CREATE INDEX IF NOT EXISTS idx_msa_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code)
            ';
    RAISE NOTICE 'Index % created.', 'idx_msa_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                   CREATE UNIQUE INDEX  IF NOT EXISTS unq_idx_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code, zip_code, date_of_record )
                        ';
    RAISE NOTICE 'Index % created.', 'unq_idx_' || quote_ident(summarized_view_name);

END;
$$
LANGUAGE plpgsql;

CREATE OR REPLACE PROCEDURE safe_rename_and_drop_materialized_view(
    p_old_view_name TEXT,
    p_new_view_name TEXT,
    p_esquema TEXT DEFAULT 'public',
    p_backup_suffix TEXT DEFAULT '_backup_temp'
) AS $$
DECLARE
    v_temp_name TEXT;
    v_old_exists BOOLEAN := FALSE;
    v_new_exists BOOLEAN := FALSE;
    v_temp_exists BOOLEAN := FALSE;
BEGIN
    -- Generar nombre temporal único
    v_temp_name := p_old_view_name || p_backup_suffix || '_' || extract(epoch from now())::bigint;

    -- Verificar existencia de las vistas
    SELECT EXISTS(
        SELECT 1 FROM pg_matviews
        WHERE schemaname = p_esquema AND matviewname = p_old_view_name
    ) INTO v_old_exists;

    SELECT EXISTS(
        SELECT 1 FROM pg_matviews
        WHERE schemaname = p_esquema AND matviewname = p_new_view_name
    ) INTO v_new_exists;

    RAISE NOTICE 'VERIFICACION: Verificando existencia: %=%, %=%',
        p_old_view_name, v_old_exists, p_new_view_name, v_new_exists;

    IF NOT v_old_exists AND NOT v_new_exists THEN
        RAISE EXCEPTION 'ERROR: Ninguna de las dos vistas existe';
    END IF;

    IF NOT v_old_exists THEN
        RAISE EXCEPTION 'ERROR: La vista antigua (%) no existe', p_old_view_name;
    END IF;

    IF NOT v_new_exists THEN
        RAISE EXCEPTION 'ERROR: La vista nueva (%) no existe', p_new_view_name;
    END IF;

    RAISE NOTICE 'VERIFICACION EXITOSA - Ambas vistas existen';

    BEGIN
        -- Paso 1: Renombrar vista antigua a temporal
        RAISE NOTICE 'PASO_1: Renombrando % a %', p_old_view_name, v_temp_name;

        EXECUTE format('ALTER MATERIALIZED VIEW %I.%I RENAME TO %I',
                       p_esquema, p_old_view_name, v_temp_name);

        RAISE NOTICE 'PASO_1: EXITOSO';

        -- Verificar que el paso 1 funcionó
        SELECT EXISTS(
            SELECT 1 FROM pg_matviews
            WHERE schemaname = p_esquema AND matviewname = v_temp_name
        ) INTO v_temp_exists;

        IF NOT v_temp_exists THEN
            RAISE EXCEPTION 'ERROR: Falló la creación del backup temporal';
        END IF;

        -- Paso 2: Renombrar vista nueva al nombre antiguo
        RAISE NOTICE 'PASO_2: Renombrando % a %', p_new_view_name, p_old_view_name;

        EXECUTE format('ALTER MATERIALIZED VIEW %I.%I RENAME TO %I',
                       p_esquema, p_new_view_name, p_old_view_name);

        RAISE NOTICE 'PASO_2: EXITOSO';

        -- Paso 3: Renombrar vista temporal al nombre nuevo
        RAISE NOTICE 'PASO_3: Renombrando % a %', v_temp_name, p_new_view_name;

        EXECUTE format('ALTER MATERIALIZED VIEW %I.%I RENAME TO %I',
                       p_esquema, v_temp_name, p_new_view_name);

        RAISE NOTICE 'PASO_3: EXITOSO';

        -- Verificación intermedia
        SELECT EXISTS(
            SELECT 1 FROM pg_matviews
            WHERE schemaname = p_esquema AND matviewname = p_old_view_name
        ) INTO v_old_exists;

        SELECT EXISTS(
            SELECT 1 FROM pg_matviews
            WHERE schemaname = p_esquema AND matviewname = p_new_view_name
        ) INTO v_new_exists;

        IF NOT (v_old_exists AND v_new_exists) THEN
            RAISE EXCEPTION 'ERROR: Estado inconsistente después del intercambio';
        END IF;

        RAISE NOTICE 'VERIFICACION_INTERMEDIA: INTERCAMBIO COMPLETADO EXITOSAMENTE';

        -- Paso 4: Eliminar la vista que ahora tiene el nombre nuevo (que era la original)
        RAISE NOTICE 'ELIMINACION: Eliminando vista antigua (ahora con nombre %)', p_new_view_name;

        EXECUTE format('DROP MATERIALIZED VIEW %I.%I', p_esquema, p_new_view_name);

        -- Verificar que se eliminó correctamente
        SELECT EXISTS(
            SELECT 1 FROM pg_matviews
            WHERE schemaname = p_esquema AND matviewname = p_new_view_name
        ) INTO v_new_exists;

        IF NOT v_new_exists THEN
            RAISE NOTICE 'ELIMINACION: VISTA ANTIGUA ELIMINADA EXITOSAMENTE';
        ELSE
            RAISE EXCEPTION 'ERROR: No se pudo eliminar la vista antigua';
        END IF;

        RAISE NOTICE 'PROCESO COMPLETADO: La vista % ahora contiene los datos de la vista nueva', p_old_view_name;

    EXCEPTION WHEN OTHERS THEN
        -- En caso de error, intentar rollback
        RAISE NOTICE 'ROLLBACK: Intentando restaurar estado original debido a error: %', SQLERRM;

        BEGIN
            -- Si existe la vista temporal, restaurar el nombre original
            IF EXISTS(SELECT 1 FROM pg_matviews WHERE schemaname = p_esquema AND matviewname = v_temp_name) THEN
                EXECUTE format('ALTER MATERIALIZED VIEW %I.%I RENAME TO %I',
                               p_esquema, v_temp_name, p_old_view_name);
                RAISE NOTICE 'ROLLBACK: ROLLBACK EXITOSO - Estado original restaurado';
            ELSE
                RAISE NOTICE 'ROLLBACK: ERROR - Verificar manualmente el estado de las vistas';
            END IF;
        EXCEPTION WHEN OTHERS THEN
            RAISE EXCEPTION 'ERROR CRITICO EN ROLLBACK: %', SQLERRM;
        END;

        -- Re-lanzar la excepción original
        RAISE;
    END;
END;
$$ LANGUAGE plpgsql;


alter procedure safe_rename_and_drop_materialized_view(text, text, text, text) owner to rent;

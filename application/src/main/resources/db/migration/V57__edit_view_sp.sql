create or replace procedure create_monthly_summarized_view(IN listing_view_name text, IN effective_view_name text, IN summarized_view_name text)
    language plpgsql
as
$$
DECLARE
    sql TEXT := '';
BEGIN
    -- Skip creation if view already exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews WHERE matviewname = summarized_view_name
    ) THEN
        RAISE NOTICE 'View % already exists. Skipping.', summarized_view_name;
        RETURN;
    END IF;

    -- Build dynamic SQL
    sql := 'CREATE MATERIALIZED VIEW IF NOT EXISTS ' || quote_ident(summarized_view_name) || ' AS ' ||
           ' with prop_data as (select rl.property_id,
                           rl.zip_code,
                           rl.msa_code,
                           rl.date_of_record,
                           sum(rl.total_records)             as total_records,
                           --asking
                           min(rl.min_rent)                  as asking_min,
                           sum(rl.rent_sum)                  as asking_sum,
                           max(rl.max_rent)                  as asking_max,
                           --effective
                           min(er.min_rent)                  as effective_min,
                           sum(er.rent_sum)                  as effective_sum,
                           max(er.max_rent)                  as effective_max,
                           -- square_feet
                           min(rl.min_sft)                   as sft_min,
                           sum(rl.sft_sum)                   as sft_sum,
                           max(rl.max_sft)                   as sft_max,
                           -- listing_days
                           sum(rl.day_listing_since_publish) as day_listing_since_publish,
                           sum(rl.total_day_listing)         as total_day_listing
                    from '|| quote_ident(listing_view_name) ||' rl
                            inner join '|| quote_ident(effective_view_name) ||' er
                                       on rl.property_id = er.property_id
                                           and rl.bedrooms = er.bedrooms
                                           and rl.date_of_record = er.date_of_record
                   group by rl.property_id,
                            rl.zip_code,
                            rl.msa_code,
                            rl.date_of_record)
SELECT prop_data.msa_code,
       prop_data.zip_code,
       prop_data.date_of_record,
       sum(prop_data.total_records)                                                    AS total_records,
       min(prop_data.asking_min)                                                       AS asking_min,
       sum(prop_data.asking_sum)                                                       AS asking_sum,
       max(prop_data.asking_max)                                                       AS asking_max,
       min(prop_data.effective_min)                                                    AS effective_min,
       sum(prop_data.effective_sum)                                                    AS effective_sum,
       max(prop_data.effective_max)                                                    AS effective_max,
       min(prop_data.sft_min)                                                          AS sft_min,
       sum(prop_data.sft_sum)                                                          AS sft_sum,
       max(prop_data.sft_max)                                                          AS sft_max,
       sum(prop_data.day_listing_since_publish)                                        AS day_listing_since_publish,
       sum(prop_data.total_day_listing)                                                AS total_day_listing,
       avg(
               CASE
                   WHEN mf.unit_quantity > 1 AND mf.unit_quantity::numeric > prop_data.total_records
                       THEN prop_data.total_records / mf.unit_quantity::numeric
                   ELSE NULL::numeric
                   END)                                                                AS exposure,
       avg(CASE
               WHEN (prop_data.asking_sum - prop_data.effective_sum) > 0::numeric
                   THEN (prop_data.asking_sum - prop_data.effective_sum) / prop_data.asking_sum
           END
       )
                                                                                       AS concession_rate,
       avg(CASE
               WHEN (prop_data.asking_sum - prop_data.effective_sum) > 0::numeric
                   THEN (prop_data.asking_sum - prop_data.effective_sum) / prop_data.total_records
           END
       )
                                                                                       AS concession_amount,
       avg((prop_data.asking_sum - prop_data.effective_sum) / prop_data.asking_sum)    AS concession_rate_all_props,
       avg((prop_data.asking_sum - prop_data.effective_sum) / prop_data.total_records) AS concession_amount_all_props,
       sum(
               CASE
                   WHEN (prop_data.asking_sum - prop_data.effective_sum) > 0::numeric THEN 1
                   ELSE 0
                   END)                                                                AS props_with_concession,
       count(DISTINCT prop_data.property_id)                                           AS total_props
FROM prop_data
         JOIN multifamily_properties mf ON mf.id::text = prop_data.property_id::text
GROUP BY prop_data.msa_code, prop_data.zip_code, prop_data.date_of_record';

    -- Execute SQL
    RAISE NOTICE 'Creating materialized view: %', summarized_view_name;
    EXECUTE sql;


    EXECUTE '
                  CREATE INDEX IF NOT EXISTS idx_zip_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (zip_code)
                ';
    RAISE NOTICE 'Index % created.', 'idx_zip_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                CREATE INDEX IF NOT EXISTS idx_msa_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code)
            ';
    RAISE NOTICE 'Index % created.', 'idx_msa_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                   CREATE UNIQUE INDEX  IF NOT EXISTS unq_idx_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code, zip_code, date_of_record )
                        ';
    RAISE NOTICE 'Index % created.', 'unq_idx_' || quote_ident(summarized_view_name);

END;
$$;

alter procedure create_monthly_summarized_view(text, text, text) owner to rent;
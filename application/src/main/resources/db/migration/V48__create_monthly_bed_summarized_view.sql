CREATE OR REPLACE PROCEDURE create_monthly_effective_rent_view(IN listing_view_name text,
                                                               IN effective_view_name text,
                                                               IN summarized_view_name text)
AS
$$
DECLARE
    sql TEXT := '';
BEGIN
    -- Skip creation if view already exists
    IF EXISTS (
        SELECT 1 FROM pg_matviews WHERE matviewname = summarized_view_name
    ) THEN
        RAISE NOTICE 'View % already exists. Skipping.', summarized_view_name;
        RETURN;
    END IF;

    -- Build dynamic SQL
    sql := 'CREATE MATERIALIZED VIEW IF NOT EXISTS ' || quote_ident(summarized_view_name) || ' AS ' ||
           'select rl.msa_code,
       rl.zip_code,
       rl.date_of_record,
       rl.bedrooms,
       sum(rl.total_records)                                                           as total_records,
       --asking
       min(rl.min_rent)                                                                as asking_min,
       sum(rl.rent_sum)                                                                as asking_sum,
       max(rl.max_rent)                                                                as asking_max,
       --effective
       min(er.min_rent)                                                                as effective_min,
       sum(er.rent_sum)                                                                as effective_sum,
       max(er.max_rent)                                                                as effective_max,
       -- square_feet
       min(rl.min_sft)                                                                 as sft_min,
       sum(rl.sft_sum)                                                                 as sft_sum,
       max(rl.max_sft)                                                                 as sft_max,
       -- listing_days
       sum(rl.day_listing_since_publish)                                               as day_listing_since_publish,
       sum(rl.total_day_listing)                                                       as total_day_listing,
       -- metrics
       avg((rl.rent_sum::numeric - er.rent_sum ::numeric) / rl.rent_sum::numeric)      as concession_rate,
       avg((rl.rent_sum::numeric - er.rent_sum ::numeric) / rl.total_records::numeric) as concession_amount,
       sum(case when rl.rent_sum - er.rent_sum > 0 then 1 else 0 end)                  as props_with_concession,
       count(distinct rl.property_id)                                                  as total_props
from '|| quote_ident(listing_view_name) || ' rl
                            inner join '|| quote_ident(effective_view_name) ||' er
                    on rl.property_id = er.property_id
                        and rl.bedrooms = er.bedrooms
                        and rl.date_of_record = er.date_of_record
group by rl.zip_code,
         rl.msa_code,
         rl.date_of_record,
         rl.bedrooms WITH NO DATA;';

    -- Execute SQL
    RAISE NOTICE 'Creating materialized view: %', summarized_view_name;
    EXECUTE sql;

    EXECUTE '
                  CREATE INDEX IF NOT EXISTS idx_zip_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (zip_code)
                ';
    RAISE NOTICE 'Index % created.', 'idx_zip_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                CREATE INDEX IF NOT EXISTS idx_msa_code_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code)
            ';
    RAISE NOTICE 'Index % created.', 'idx_msa_code_' || quote_ident(summarized_view_name);

    EXECUTE '
                   CREATE UNIQUE INDEX  IF NOT EXISTS unq_idx_' || quote_ident(summarized_view_name) ||
            ' ON ' || quote_ident(summarized_view_name) || ' (msa_code, zip_code, date_of_record, bedrooms)
                        ';
    RAISE NOTICE 'Index % created.', 'unq_idx_' || quote_ident(summarized_view_name);

END;
$$
LANGUAGE plpgsql;

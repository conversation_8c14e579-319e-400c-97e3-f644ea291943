create or replace procedure refresh_materialize_view_concurrently_without_date_of_record(IN view_pattern text)
    language plpgsql
as
$$
DECLARE
    matview_name TEXT;
    start_time TIMESTAMP;
    end_time TIMESTAMP;
    duration INTERVAL;
    total_start_time TIMESTAMP;
    total_end_time TIMESTAMP;
    total_duration INTERVAL;
    view_count INTEGER := 0;
BEGIN
    -- Auto-append % if not present for convenience

    total_start_time := clock_timestamp();
    RAISE NOTICE 'Starting refresh of materialized views matching pattern: %', view_pattern;
    RAISE NOTICE 'Process started at: %', total_start_time;
    FOR matview_name IN
        SELECT matviewname
        FROM pg_matviews
        WHERE matviewname LIKE view_pattern
        ORDER BY matviewname
        LOOP
            view_count := view_count + 1;
            start_time := clock_timestamp();
            RAISE NOTICE '[%] Starting refresh of: %', view_count, matview_name;
            EXECUTE 'REFRESH MATERIALIZED VIEW CONCURRENTLY ' || quote_ident(matview_name);
            end_time := clock_timestamp();
            duration := end_time - start_time;
            RAISE NOTICE '[%] Completed refresh of: %', view_count, matview_name;
            RAISE NOTICE '[%] Duration: % (% seconds)', view_count, duration, EXTRACT(EPOCH FROM duration);
            RAISE NOTICE '----------------------------------------';

            BEGIN


                INSERT INTO rent_summarized_records_status (view_name, last_date_of_record, created_at, updated_at)
                VALUES (matview_name, current_date, current_timestamp, current_timestamp)
                ON CONFLICT (view_name)
                DO UPDATE SET
                view_name = excluded.view_name,
                last_date_of_record = EXCLUDED.last_date_of_record,
                updated_at = EXCLUDED.updated_at;
            END;
        END LOOP;

    total_end_time := clock_timestamp();
    total_duration := total_end_time - total_start_time;

    RAISE NOTICE 'Total views refreshed: %', view_count;

    RAISE NOTICE 'Process completed at: %', total_end_time;

    IF view_count = 0 THEN
        RAISE NOTICE 'WARNING: No materialized views found matching pattern: %', view_pattern;
    END IF;
END $$;



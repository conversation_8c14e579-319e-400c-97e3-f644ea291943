package com.keyway.application.koin.modules

import com.keyway.adapters.handlers.rest.HealthCheckHandler
import com.keyway.adapters.handlers.rest.concessions.GetConcessionTypesDistributionHandler
import com.keyway.adapters.handlers.rest.concessions.GetPropertiesConcessionsHandler
import com.keyway.adapters.handlers.rest.concessions.GetPropertiesConcessionsV2Handler
import com.keyway.adapters.handlers.rest.historical.GetHistoricalConcessionByIds
import com.keyway.adapters.handlers.rest.historical.GetHistoricalDaysOnMarketHandler
import com.keyway.adapters.handlers.rest.historical.GetHistoricalExposureByIdsHandler
import com.keyway.adapters.handlers.rest.historical.GetHistoricalRentsByIds
import com.keyway.adapters.handlers.rest.listings.GetFutureAvailabilityHandler
import com.keyway.adapters.handlers.rest.listings.GetPropertiesListingsHandler
import com.keyway.adapters.handlers.rest.listings.GetPropertyListingsHandler
import com.keyway.adapters.handlers.rest.metrics.GetGeoMetricsHandler
import com.keyway.adapters.handlers.rest.metrics.GetPropertyMetricsHandler
import com.keyway.adapters.handlers.rest.suggestion.GetRentSuggestionHandler
import com.keyway.adapters.handlers.rest.units.GetPropertiesLastListingHandler
import com.keyway.adapters.handlers.rest.units.GetPropertyLastListingHandler
import com.keyway.application.koin.ModuleConstants
import com.keyway.application.router.concession.GetConcessionTypesDistributionRouter
import com.keyway.application.router.concession.GetPropertiesConcessionsRouter
import com.keyway.application.router.concession.HistoricalConcessionRouter
import com.keyway.application.router.health.HealthCheckRouter
import com.keyway.application.router.rent.GeoRentMetricsRouter
import com.keyway.application.router.rent.GetPropertiesLastListingRouter
import com.keyway.application.router.rent.GetPropertiesListingsRouter
import com.keyway.application.router.rent.GetPropertyLastListingRouter
import com.keyway.application.router.rent.GetPropertyListingsRouter
import com.keyway.application.router.rent.HistoricalDaysOnMarketRouter
import com.keyway.application.router.rent.HistoricalExposureRouter
import com.keyway.application.router.rent.HistoricalRentRouter
import com.keyway.application.router.rent.RentMetricsByPropertyIdRouter
import com.keyway.application.router.rent.RentMetricsRouter
import com.keyway.application.router.rent.RentSuggestionRouter
import com.keyway.application.utils.koin.createInstanceBy
import org.koin.core.module.Module
import org.koin.core.qualifier.named
import org.koin.dsl.module

object RoutingModule : KoinModule {
    override fun get(): Module =
        module(createdAtStart = true) {
            // Handlers
            single { createInstanceBy(::HealthCheckHandler) }
            single { createInstanceBy(::GetPropertyLastListingHandler) }
            single { createInstanceBy(::GetPropertyListingsHandler) }
            single { createInstanceBy(::GetPropertiesListingsHandler) }
            single { createInstanceBy(::GetPropertiesLastListingHandler) }
            single { createInstanceBy(::GetPropertyMetricsHandler) }
            single { createInstanceBy(::GetHistoricalRentsByIds) }
            single { createInstanceBy(::GetPropertiesConcessionsHandler) }
            single { createInstanceBy(::GetPropertiesConcessionsV2Handler) }
            single { createInstanceBy(::GetRentSuggestionHandler) }
            single { createInstanceBy(::GetGeoMetricsHandler) }
            single { createInstanceBy(::GetFutureAvailabilityHandler) }
            single { createInstanceBy(::GetHistoricalConcessionByIds) }
            single { createInstanceBy(::GetConcessionTypesDistributionHandler) }
            single { createInstanceBy(::GetHistoricalExposureByIdsHandler) }
            single { createInstanceBy(::GetHistoricalDaysOnMarketHandler) }

            // Routers
            single(named(ModuleConstants.ROUTES)) {
                setOf(
                    createInstanceBy(::HealthCheckRouter),
                    createInstanceBy(::GetPropertyLastListingRouter),
                    createInstanceBy(::GetPropertyListingsRouter),
                    createInstanceBy(::GetPropertiesListingsRouter),
                    createInstanceBy(::GetPropertiesLastListingRouter),
                    createInstanceBy(::RentMetricsRouter),
                    createInstanceBy(::RentMetricsByPropertyIdRouter),
                    createInstanceBy(::HistoricalRentRouter),
                    createInstanceBy(::GetPropertiesConcessionsRouter),
                    createInstanceBy(::GeoRentMetricsRouter),
                    createInstanceBy(::RentSuggestionRouter),
                    createInstanceBy(::HistoricalConcessionRouter),
                    createInstanceBy(::GetConcessionTypesDistributionRouter),
                    createInstanceBy(::HistoricalExposureRouter),
                    createInstanceBy(::HistoricalDaysOnMarketRouter),
                )
            }
        }
}

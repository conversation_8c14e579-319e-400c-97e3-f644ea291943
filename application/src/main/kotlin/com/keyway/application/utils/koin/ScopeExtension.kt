package com.keyway.application.utils.koin

import org.koin.core.qualifier.Qualifier
import org.koin.core.scope.Scope
import kotlin.reflect.KClass
import kotlin.reflect.KFunction
import kotlin.reflect.jvm.jvmErasure

inline fun <reified T> Scope.createInstanceBy(
    function: KFunction<T>,
    qualifiers: Map<KClass<*>, Qualifier> = mapOf(),
): T =
    function.parameters
        .map { param ->
            param.type.jvmErasure.let { paramKlass ->
                get<Any>(
                    clazz = paramKlass,
                    qualifier = qualifiers[paramKlass],
                )
            }
        }.toTypedArray()
        .let { function.call(*it) }

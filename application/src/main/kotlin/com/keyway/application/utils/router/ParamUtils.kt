package com.keyway.application.utils.router

import com.keyway.adapters.handlers.rest.historical.GetHistoricalRentsByIds
import com.keyway.adapters.handlers.utils.HandlersUtils
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.UnitCondition
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import java.time.LocalDate

object ParamUtils {
    const val PROP_IDS_PARAM_NAME = "propertyIds"
    const val PROP_IDS_DESC =
        "List with property ids, " +
            "the max amount of properties allowed to " +
            "search is ${HandlersUtils.MAX_PROPERTIES_ALLOWED} ids"

    const val DATE_FROM_PARAM_NAME = "dateFrom"
    const val DATE_FROM_DESC = "Start date for the search. Must follow the pattern YYYY-MM-DD. If not provided, defaults to 30 days before the request date."

    const val DATE_TO_PARAM_NAME = "dateTo"
    const val DATE_TO_DESC = "End date for the search. Must follow the pattern YYYY-MM-DD. If not provided, defaults to the request date."

    const val ACTIVE_PARAM_NAME = "isActive"
    const val ACTIVE_DESC = "Flag to get only the active concessions. If it is not provided, will be returning all concessions."

    const val BENEFIT_PARAM_NAME = "benefit"
    const val BENEFIT_DESC = """Benefit to filter results. Available values are: ADMINISTRATION, APPLICATION, MOVE_IN, RENT, SECURITY, DEPOSIT or GIFT_CARD."""

    const val BENEFIT_TYPE_PARAM_NAME = "type"
    const val BENEFIT_TYPE_DESC = """Benefit type to filter results. Available values are:  FREE, DISCOUNT or PRO_RATED"""

    const val UNIT_CONDITION_PARAM_NAME = "unitCondition"
    const val UNIT_CONDITION_DESC = """Unit Condition to filter results. Available values are:  RENO or NON_RENO. If not provided, all units will be included by default."""

    const val RENT_TYPE = "rentType"
    const val PERIODICITY = "periodicity"

    const val PROPERTY_ID_PARAM_NAME = "propertyId"
    const val PROPERTY_ID_PARAM_NAME_DESC = "Id for property"
    const val UNIT_ID_PARAM_NAME = "unitId"
    const val LISTING_TYPE_PARAM_NAME = "listingType"
    const val FLOOR_PLAN_PARAM_NAME = "floorPlan"
    const val BEDROOMS_PARAM_NAME = "bedrooms"
    const val BATHROOMS_PARAM_NAME = "bathrooms"

    const val MSA_CODE_PARAM = "msaCode"
    const val ZIP_CODE_PARAM = "zipCode"

    const val ZIP_TAG = "zipMetrics"
    const val MSA_TAG = "msaMetrics"

    fun RouteConfig.propIdsBaseParameterDoc(block: RequestConfig.() -> Unit = {}) =
        request {
            queryParameter<List<String>>(PROP_IDS_PARAM_NAME) {
                required = true
                description = PROP_IDS_DESC
            }
            queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                required = false
                description = DATE_FROM_DESC
            }
            queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                required = false
                description = DATE_TO_DESC
            }
            queryParameter<UnitCondition>(UNIT_CONDITION_PARAM_NAME) {
                required = false
                description = UNIT_CONDITION_DESC
            }
            block()
        }

    fun RouteConfig.propIdBaseParameterDoc(block: RequestConfig.() -> Unit = {}) =
        request {
            pathParameter<String>(PROPERTY_ID_PARAM_NAME) {
                required = true
                description = PROPERTY_ID_PARAM_NAME_DESC
            }
            queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                required = false
                description = DATE_FROM_DESC
            }
            queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                required = false
                description = DATE_TO_DESC
            }
            queryParameter<UnitCondition>(UNIT_CONDITION_PARAM_NAME) {
                required = false
                description = UNIT_CONDITION_DESC
            }
            block()
        }

    fun RouteConfig.historicalBaseDocumentation(block: RequestConfig.() -> Unit = {}) =
        request {
            block()
            queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                required = false
                description = DATE_FROM_DESC
            }
            queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                required = false
                description = DATE_TO_DESC
            }
            queryParameter<HistoricalPeriodicity>(PERIODICITY) {
                required = false
                description =
                    """
                    Periodicity of the metric, default value will be calculated based on period.
                    
                    Max periods: 
                    - ${HistoricalPeriodicity.DAILY} ${GetHistoricalRentsByIds.Companion.MAX_DAILY_AMOUNT} days, 
                    - ${HistoricalPeriodicity.WEEKLY} ${GetHistoricalRentsByIds.Companion.MAX_WEEKLY_AMOUNT} weeks, 
                    - ${HistoricalPeriodicity.MONTHLY} ${GetHistoricalRentsByIds.Companion.MAX_MONTHLY_AMOUNT} months
                    """.trimIndent()
            }
        }
}

package com.keyway.application.utils.router

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.utils.PropertyIdValidator
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.type.ComplexType
import io.ktor.server.application.ApplicationCall
import io.ktor.server.plugins.MissingRequestParameterException
import io.ktor.server.request.receiveText
import java.math.BigDecimal
import java.time.LocalDate

object RequestUtils {
    private fun <T> getOrFail(
        parameterName: String,
        block: (String) -> T?,
    ): T = block(parameterName) ?: throw MissingRequestParameterException(parameterName)

    fun ApplicationCall.getStringPathParameter(parameterName: String): String? = this.parameters[parameterName]

    fun ApplicationCall.getStringPathParameterOrFail(parameterName: String): String =
        getOrFail(parameterName) {
            this.getStringPathParameter(it)
        }

    fun ApplicationCall.getStringQueryParameter(parameterName: String): String? = this.request.queryParameters[parameterName]

    fun ApplicationCall.getStringQueryParameterOrFail(parameterName: String): String =
        getOrFail(parameterName) {
            this.getStringQueryParameter(it)
        }

    fun ApplicationCall.getListStringQueryParameter(
        parameterName: String,
        delimiter: String = ",",
    ): List<String>? =
        this.request.queryParameters.getAll(parameterName)?.let { list ->
            list
                .map { it.trim().split(delimiter) }
                .flatten()
                .filter { it.isNotBlank() }
        }

    fun ApplicationCall.getListStringQueryParameterOrFail(
        parameterName: String,
        delimiter: String = ",",
    ): List<String> =
        getOrFail(parameterName) {
            this.getListStringQueryParameter(it, delimiter)
        }

    fun ApplicationCall.getIntQueryParameter(parameterName: String): Int? = this.request.queryParameters[parameterName]?.toIntOrNull()

    fun ApplicationCall.getIntQueryParameterOrFail(parameterName: String): Int =
        getOrFail(parameterName) {
            getIntQueryParameter(it)
        }

    fun ApplicationCall.getIntPathParameter(parameterName: String): Int? = this.parameters[parameterName]?.toIntOrNull()

    fun ApplicationCall.getIntPathParameterOrFail(parameterName: String): Int =
        getOrFail(parameterName) {
            this.getIntPathParameter(it)
        }

    fun ApplicationCall.getBigDecimalQueryParameter(parameterName: String): BigDecimal? = this.request.queryParameters[parameterName]?.toBigDecimalOrNull()

    fun ApplicationCall.getBigDecimalQueryParameterOrFail(parameterName: String): BigDecimal =
        getOrFail(parameterName) {
            getBigDecimalQueryParameter(it)
        }

    fun ApplicationCall.getBigDecimalPathParameter(parameterName: String): BigDecimal? = this.parameters[parameterName]?.toBigDecimalOrNull()

    fun ApplicationCall.getBigDecimalPathParameterOrFail(parameterName: String): BigDecimal =
        getOrFail(parameterName) {
            getBigDecimalPathParameter(it)
        }

    fun ApplicationCall.getLocalDateQueryParameter(parameterName: String): LocalDate? =
        runCatching {
            this.request.queryParameters[parameterName]?.let { LocalDate.parse(it) }
        }.getOrElse {
            throw MissingRequestParameterException(parameterName)
        }

    fun ApplicationCall.getLocalDateQueryParameterOrFail(parameterName: String): LocalDate =
        getOrFail(parameterName) {
            getLocalDateQueryParameter(it)
        }

    fun ApplicationCall.getBooleanQueryParameter(parameterName: String): Boolean? = this.request.queryParameters[parameterName]?.let { it.toBoolean() }

    fun ApplicationCall.getLocalDatePathParameter(parameterName: String): LocalDate? =
        runCatching {
            this.parameters[parameterName]?.let { LocalDate.parse(it) }
        }.getOrElse {
            throw MissingRequestParameterException(parameterName)
        }

    fun ApplicationCall.getLocalDatePathParameterOrFail(parameterName: String): LocalDate =
        getOrFail(parameterName) {
            getLocalDatePathParameter(it)
        }

    fun ApplicationCall.getPropertyId(): String =
        getStringPathParameterOrFail(ParamUtils.PROPERTY_ID_PARAM_NAME).takeIf {
            PropertyIdValidator.isValid(it)
        } ?: throw BadRequestException("INVALID ${ParamUtils.PROPERTY_ID_PARAM_NAME}")

    fun ApplicationCall.getPropertyIds(): Set<String> =
        this
            .getListStringQueryParameterOrFail(PROP_IDS_PARAM_NAME)
            .map {
                it.takeIf { PropertyIdValidator.isValid(it) }
                    ?: throw BadRequestException("INVALID ${ParamUtils.PROPERTY_ID_PARAM_NAME} $it")
            }.toSet()

    fun ApplicationCall.getHeader(name: String): String? = this.request.headers[name]

    fun ApplicationCall.validateQueryDateRange(
        dateFromParam: String,
        dateToParam: String,
    ) {
        this.getLocalDateQueryParameter(dateFromParam)?.let { dateFrom ->
            this.getLocalDateQueryParameter(dateToParam)?.let { dateTo ->
                if (dateFrom.isAfter(dateTo)) {
                    throw BadRequestException("INVALID $dateFromParam must be before $dateToParam")
                }
            }
        }
    }

    suspend fun <T> ApplicationCall.readBody(clazz: Class<T>): T = JsonMapper.decode(this.receiveText(), clazz)

    suspend fun <T> ApplicationCall.readBody(complexType: ComplexType<T>): T = JsonMapper.decode(this.receiveText(), complexType)
}

package com.keyway.application.router.rent

import com.keyway.adapters.dtos.listings.UnitRentSuggestions
import com.keyway.adapters.handlers.rest.suggestion.GetRentSuggestionHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.application.utils.router.RequestUtils.getHeader
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getPropertyIds
import com.keyway.application.utils.router.RequestUtils.validateQueryDateRange
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import java.time.LocalDate

class RentSuggestionRouter(
    private val getRentSuggestionHandler: GetRentSuggestionHandler,
) : Router {
    companion object {
        const val BASE_URL = "/multifamily/{$PROPERTY_ID_PARAM_NAME}/unit-rent-suggestions"
        const val TAG = "propertyRentSuggestion"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                BASE_URL,
                {
                    tags = listOf(TAG)
                    summary = "Property listings rent suggestion"
                    description =
                        """
                        Retrieves a suggested rent for each property listing based on the rents of the given properties listings
                        """.trimIndent()
                    response {
                        HttpStatusCode.OK to { body<UnitRentSuggestions>() }
                    }
                    request {
                        pathParameter<String>(PROPERTY_ID_PARAM_NAME) {
                            description = "Id for property"
                            required = true
                        }
                        queryParameter<List<String>>(PROP_IDS_PARAM_NAME) {
                            description = "Id of the properties to get the similarity units from"
                            required = true
                        }
                        queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                            description = DATE_FROM_DESC
                            required = true
                        }
                        queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                            description = DATE_TO_DESC
                            required = true
                        }
                    }
                },
            ) {
                val httpInput =
                    GetRentSuggestionHandler.Input(
                        organizationId = call.getHeader("x-organization-id"),
                        basePropertyId = call.getPropertyId(),
                        propertiesIds = call.getPropertyIds(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    )
                call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
                call.respond(getRentSuggestionHandler.invoke(httpInput))
            }
        }
    }
}

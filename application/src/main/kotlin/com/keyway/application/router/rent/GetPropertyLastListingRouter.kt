package com.keyway.application.router.rent

import com.keyway.adapters.dtos.listings.RentListingInput
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.handlers.rest.units.GetPropertyLastListingHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.validateQueryDateRange
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing

class GetPropertyLastListingRouter(
    private val handler: GetPropertyLastListingHandler,
) : Router {
    companion object {
        const val BASE_URL = "/multifamily/{$PROPERTY_ID_PARAM_NAME}/units"
        const val TAG = "propertyUnitRent"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                BASE_URL,
                {
                    tags = listOf(TAG)
                    summary = "Property unit rent data"
                    description =
                        """
                        Allows obtaining detailed information about the units of a specific rental property, including rent, deposit, and the listed time period. 
                        It ensures returning a single listing per unit and the most recent one available in the database.
                        """.trimIndent()
                    response {
                        HttpStatusCode.OK to { body<RentListingsResponse>() }
                    }
                    request {
                        pathParameter<String>(PROPERTY_ID_PARAM_NAME) {
                            description = "Id for property"
                            required = true
                        }
                        queryParameter<String>(DATE_FROM_PARAM_NAME) {
                            description = DATE_FROM_DESC
                            required = false
                        }
                        queryParameter<String>(DATE_TO_PARAM_NAME) {
                            description = DATE_TO_DESC
                            required = false
                        }
                    }
                },
            ) {
                val httpInput =
                    RentListingInput(
                        propertyId = call.getPropertyId(),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    )
                call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
                call.respond(handler.invoke(httpInput))
            }
        }
    }
}

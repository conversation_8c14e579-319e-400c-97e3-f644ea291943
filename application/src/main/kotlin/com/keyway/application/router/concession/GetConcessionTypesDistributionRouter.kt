package com.keyway.application.router.concession

import com.keyway.adapters.handlers.rest.concessions.GetConcessionTypesDistributionHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.MSA_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.MSA_TAG
import com.keyway.application.utils.router.ParamUtils.ZIP_CODE_PARAM
import com.keyway.application.utils.router.ParamUtils.ZIP_TAG
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getStringPathParameterOrFail
import com.keyway.application.utils.router.RequestUtils.validateQueryDateRange
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.concessions.ConcessionTypesDistribution
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import java.time.LocalDate

class
GetConcessionTypesDistributionRouter(
    private val getConcessionTypesDistribution: GetConcessionTypesDistributionHandler,
) : Router {
    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/msa-codes/{$MSA_CODE_PARAM}/concession-types-distribution",
                {
                    tags = listOf(MSA_TAG)
                    summary = "Concession Types Distribution by Msa Code"
                    request {
                        pathParameter<String>(MSA_CODE_PARAM) {
                            required = true
                        }
                        queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                            required = false
                            description = DATE_FROM_DESC
                        }
                        queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                            required = false
                            description = DATE_TO_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ConcessionTypesDistribution>() }
                    }
                },
            ) {
                call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
                call.respond(
                    getConcessionTypesDistribution(
                        idType = IdType.MSA,
                        id = call.getStringPathParameterOrFail(MSA_CODE_PARAM),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    ),
                )
            }

            get(
                "/zip-codes/{$ZIP_CODE_PARAM}/concession-types-distribution",
                {
                    tags = listOf(ZIP_TAG)
                    summary = "Concession Types Distribution by Zip Code"
                    request {
                        pathParameter<String>(ZIP_CODE_PARAM) {
                            required = true
                        }
                        queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                            required = false
                            description = DATE_FROM_DESC
                        }
                        queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                            required = false
                            description = DATE_TO_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ConcessionTypesDistribution>() }
                    }
                },
            ) {
                call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
                call.respond(
                    getConcessionTypesDistribution(
                        idType = IdType.ZIP_CODE,
                        id = call.getStringPathParameterOrFail(ZIP_CODE_PARAM),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                    ),
                )
            }
        }
    }
}

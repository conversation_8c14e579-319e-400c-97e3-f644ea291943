package com.keyway.application.router.rent

import com.keyway.adapters.dtos.metrics.historical.HistoricalDaysOnMarketSummary
import com.keyway.adapters.handlers.rest.historical.GetHistoricalDaysOnMarketHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PERIODICITY
import com.keyway.application.utils.router.ParamUtils.historicalBaseDocumentation
import com.keyway.application.utils.router.RequestUtils.getIntQueryParameter
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import io.github.smiley4.ktoropenapi.config.RequestConfig
import io.github.smiley4.ktoropenapi.config.RouteConfig
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import java.time.LocalDate

class HistoricalDaysOnMarketRouter(
    private val getHistoricalDaysOnMarket: GetHistoricalDaysOnMarketHandler,
) : Router {
    companion object {
        const val TAG = "historical"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/multifamily/{${ParamUtils.PROPERTY_ID_PARAM_NAME}}/historical-days-on-market-summary",
                {
                    tags = listOf(TAG)
                    summary = "Historical Days On Market Summary"
                    historicalDocumentation {
                        pathParameter<String>(ParamUtils.PROPERTY_ID_PARAM_NAME) {
                            required = true
                            description = ParamUtils.PROPERTY_ID_PARAM_NAME_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<HistoricalDaysOnMarketSummary>() }
                    }
                },
            ) {
                call.getPropertyId().let { propId ->
                    call.respond(
                        getHistoricalDays(
                            ids = setOf(call.getPropertyId()),
                            idType = IdType.PROPERTY,
                        ),
                    )
                }
            }
        }
    }

    private suspend fun RoutingContext.getHistoricalDays(
        ids: Set<String>,
        idType: IdType,
    ): HistoricalDaysOnMarketSummary =
        getHistoricalDaysOnMarket(
            ids = ids,
            idType = idType,
            dateFrom =
                call.getLocalDateQueryParameter(ParamUtils.DATE_FROM_PARAM_NAME)
                    ?: LocalDate.now().minusDays(30),
            dateTo =
                call.getLocalDateQueryParameter(ParamUtils.DATE_TO_PARAM_NAME)
                    ?: LocalDate.now(),
            periodicity =
                call
                    .getStringQueryParameter(PERIODICITY)
                    ?.let { HistoricalPeriodicity.valueOf(it.uppercase()) },
            bedrooms = call.getIntQueryParameter(BEDROOMS_PARAM_NAME),
        )

    private fun RouteConfig.historicalDocumentation(block: RequestConfig.() -> Unit = {}) =
        historicalBaseDocumentation {
            block()
            queryParameter<Int>(BEDROOMS_PARAM_NAME) {
                required = false
            }
        }
}

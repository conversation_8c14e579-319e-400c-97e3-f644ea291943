package com.keyway.application.router.rent

import com.keyway.adapters.handlers.rest.historical.GetHistoricalExposureByIdsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.historicalBaseDocumentation
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getStringPathParameterOrFail
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.application.utils.router.RequestUtils.validateQueryDateRange
import com.keyway.core.dto.HistoricalExposure
import com.keyway.core.dto.HistoricalExposureSummary
import com.keyway.core.dto.MsaHistoricalExposureSummary
import com.keyway.core.dto.ZipHistoricalExposureSummary
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.RoutingContext
import java.time.LocalDate

class HistoricalExposureRouter(
    private val getHistoricalByIds: GetHistoricalExposureByIdsHandler,
) : Router {
    companion object {
        const val TAG = "historical"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                "/multifamily/{${ParamUtils.PROPERTY_ID_PARAM_NAME}}/historical-exposure-summary",
                {
                    tags = listOf(TAG)
                    summary = "Historical Exposure Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.PROPERTY_ID_PARAM_NAME) {
                            required = true
                            description = ParamUtils.PROPERTY_ID_PARAM_NAME_DESC
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<HistoricalExposureSummary>() }
                    }
                },
            ) {
                call.getPropertyId().let { propId ->
                    call.respond(
                        getHistorical(
                            setOf(call.getPropertyId()),
                            idType = IdType.PROPERTY,
                        ),
                    )
                }
            }

            get(
                "/zip-codes/{${ParamUtils.ZIP_CODE_PARAM}}/historical-exposure-summary",
                {
                    tags = listOf(ParamUtils.ZIP_TAG)
                    summary = "ZIP Historical Exposure Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.ZIP_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<ZipHistoricalExposureSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(ParamUtils.ZIP_CODE_PARAM).let { zipCode ->
                    call.respond(
                        getHistorical(
                            setOf(zipCode),
                            idType = IdType.ZIP_CODE,
                        ),
                    )
                }
            }

            get(
                "/msa-codes/{${ParamUtils.MSA_CODE_PARAM}}/historical-exposure-summary",
                {
                    tags = listOf(ParamUtils.MSA_TAG)
                    summary = "MSA Historical Exposure Summary"
                    historicalBaseDocumentation {
                        pathParameter<String>(ParamUtils.MSA_CODE_PARAM) {
                            required = true
                        }
                    }
                    response {
                        HttpStatusCode.OK to { body<MsaHistoricalExposureSummary>() }
                    }
                },
            ) {
                call.getStringPathParameterOrFail(ParamUtils.MSA_CODE_PARAM).let { msaCode ->
                    call.respond(
                        getHistorical(
                            setOf(msaCode),
                            idType = IdType.MSA,
                        ),
                    )
                }
            }
        }
    }

    private suspend fun RoutingContext.getHistorical(
        ids: Set<String>,
        idType: IdType,
    ): HistoricalExposure {
        call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
        return getHistoricalByIds(
            ids = ids,
            idType = idType,
            dateFrom =
                call.getLocalDateQueryParameter(ParamUtils.DATE_FROM_PARAM_NAME)
                    ?: LocalDate.now().minusDays(30),
            dateTo =
                call.getLocalDateQueryParameter(ParamUtils.DATE_TO_PARAM_NAME)
                    ?: LocalDate.now(),
            periodicity =
                call
                    .getStringQueryParameter(ParamUtils.PERIODICITY)
                    ?.let { HistoricalPeriodicity.valueOf(it.uppercase()) },
        )
    }
}

package com.keyway.application.router.rent

import com.keyway.adapters.dtos.listings.ListingDataInput
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.handlers.rest.listings.GetPropertyListingsHandler
import com.keyway.application.router.Router
import com.keyway.application.utils.router.ParamUtils.BATHROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_DESC
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.FLOOR_PLAN_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.LISTING_TYPE_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROPERTY_ID_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.UNIT_ID_PARAM_NAME
import com.keyway.application.utils.router.RequestUtils.getBigDecimalQueryParameter
import com.keyway.application.utils.router.RequestUtils.getIntQueryParameter
import com.keyway.application.utils.router.RequestUtils.getLocalDateQueryParameter
import com.keyway.application.utils.router.RequestUtils.getPropertyId
import com.keyway.application.utils.router.RequestUtils.getStringQueryParameter
import com.keyway.application.utils.router.RequestUtils.validateQueryDateRange
import com.keyway.core.entities.RentListingType
import io.github.smiley4.ktoropenapi.get
import io.github.smiley4.ktoropenapi.route
import io.ktor.http.HttpStatusCode
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import java.math.BigDecimal
import java.time.LocalDate

class GetPropertyListingsRouter(
    private val getPropertyListingsHandler: GetPropertyListingsHandler,
) : Router {
    companion object {
        const val TAG = "propertyListings"
        const val BASE_URL = "/multifamily/{$PROPERTY_ID_PARAM_NAME}/listings"
    }

    override fun setUpRoutes(routing: Routing) {
        routing.route {
            get(
                BASE_URL,
                {
                    tags = listOf(TAG)
                    summary = "Property listings"
                    description =
                        """
                        Allows obtaining detailed information about the listings of specific rental property units, including rent, deposit, and the time period for which these values apply.
                        It can accept the following parameters:
                        - dateFrom: start of the period
                        - dateTo: end of the period
                        - unitId: unit ID
                        - bedrooms: number of bedrooms
                        - bathrooms: number of bathrooms
                        If dates are not provided we will return listings since 30 days ago including the futures listings.
                        
                        """.trimIndent()
                    response {
                        HttpStatusCode.OK to { body<PropertiesListingsResponse>() }
                    }
                    request {
                        pathParameter<String>(PROPERTY_ID_PARAM_NAME) {
                            description = "Id for property"
                            required = true
                        }
                        queryParameter<String>(UNIT_ID_PARAM_NAME) {
                            description = "Unit id"
                            required = false
                        }
                        queryParameter<RentListingType>(LISTING_TYPE_PARAM_NAME) {
                            description = "Listing type"
                            required = false
                        }
                        queryParameter<String>(FLOOR_PLAN_PARAM_NAME) {
                            description = "Floor plan"
                            required = false
                        }
                        queryParameter<LocalDate>(DATE_FROM_PARAM_NAME) {
                            description = DATE_FROM_DESC
                            required = false
                        }
                        queryParameter<LocalDate>(DATE_TO_PARAM_NAME) {
                            description = DATE_TO_DESC
                            required = false
                        }
                        queryParameter<Int>(BEDROOMS_PARAM_NAME) {
                            description = "Number of bedrooms"
                            required = false
                        }
                        queryParameter<BigDecimal>(BATHROOMS_PARAM_NAME) {
                            description = "Number of bathrooms"
                            required = false
                        }
                    }
                },
            ) {
                val httpInput =
                    ListingDataInput(
                        propertyId = call.getPropertyId(),
                        unitId = call.getStringQueryParameter(UNIT_ID_PARAM_NAME),
                        type =
                            call.getStringQueryParameter(LISTING_TYPE_PARAM_NAME)?.let {
                                RentListingType.valueOf(it)
                            },
                        floorPlan = call.getStringQueryParameter(FLOOR_PLAN_PARAM_NAME),
                        dateFrom = call.getLocalDateQueryParameter(DATE_FROM_PARAM_NAME),
                        dateTo = call.getLocalDateQueryParameter(DATE_TO_PARAM_NAME),
                        bedrooms = call.getIntQueryParameter(BEDROOMS_PARAM_NAME),
                        bathrooms = call.getBigDecimalQueryParameter(BATHROOMS_PARAM_NAME),
                    )
                call.validateQueryDateRange(DATE_FROM_PARAM_NAME, DATE_TO_PARAM_NAME)
                call.respond(getPropertyListingsHandler.invoke(httpInput))
            }
        }
    }
}

package com.keyway.core.dto.concessions.input

import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import java.time.LocalDate

data class SaveConcessionV2Input(
    val propertyId: String,
    val zipCode: String?,
    val msaCode: String?,
    val records: List<ConcessionRecordV2Input>,
)

data class ConcessionRecordV2Input(
    val concessionText: String,
    val benefits: List<PropertyConcessionBenefit>,
    val recordDate: LocalDate,
)

package com.keyway.core.dto

import java.math.BigDecimal

data class SummarizedMetricDto(
    val id: String,
    val totalRecords: Int,
    val averageListingsDays: BigDecimal,
    val bedrooms: Int?,
    val askingRent: GeoMetric,
    val effectiveRent: GeoMetric,
    val squareFootage: GeoMetric?,
    val totalProperties: Int,
)

data class GeoMetric(
    val min: BigDecimal,
    val max: BigDecimal,
    val average: BigDecimal,
)

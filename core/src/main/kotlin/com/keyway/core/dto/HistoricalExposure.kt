package com.keyway.core.dto

import com.keyway.core.dto.historical.HistoricalDateRange
import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalExposureSummary(
    val propertyId: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class MsaHistoricalExposureSummary(
    val msaCode: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class ZipHistoricalExposureSummary(
    val zipCode: String,
    override val avgExposureRate: BigDecimal,
    override val values: List<HistoricalExposureValue>,
) : HistoricalExposure

data class HistoricalExposureValue(
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    val avgExposureRate: BigDecimal,
) : HistoricalDateRange

sealed interface HistoricalExposure {
    val avgExposureRate: BigDecimal
    val values: List<HistoricalExposureValue>
}

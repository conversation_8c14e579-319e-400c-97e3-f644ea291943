package com.keyway.core.dto.listings.output

import com.keyway.core.dto.listings.input.AggregatedMetricType
import java.math.BigDecimal
import java.time.LocalDate

sealed interface GeoRentMetricOutput {
    val id: String
    val dateFrom: LocalDate
    val dateTo: LocalDate
    val metrics: List<GeoMetricDataOutput>
    val type: AggregatedMetricType
}

sealed interface GeoMetricDataOutput {
    val squareFootage: GeoMetricDetailOutput?
    val askingRent: GeoMetricDetailOutput
    val askingRentPSF: GeoMetricDetailOutput?
    val effectiveRent: GeoMetricDetailOutput
    val effectiveRentPSF: GeoMetricDetailOutput?
    val recordsQuantity: Int
    val averageListingDays: BigDecimal
    val totalProperties: Int
}

data class GeoMetricDetailOutput(
    val min: BigDecimal,
    val max: BigDecimal,
    val average: BigDecimal,
)

data class ByIdRentGeoMetricOutput(
    override val squareFootage: GeoMetricDetailOutput?,
    override val askingRent: GeoMetricDetailOutput,
    override val askingRentPSF: GeoMetricDetailOutput?,
    override val effectiveRent: GeoMetricDetailOutput,
    override val effectiveRentPSF: GeoMetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val totalProperties: Int,
) : GeoMetricDataOutput

data class ByIdGeoMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<ByIdRentGeoMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.BY_ID,
) : GeoRentMetricOutput

data class BedroomRentGeoMetricOutput(
    val bedrooms: Int,
    override val squareFootage: GeoMetricDetailOutput?,
    override val askingRent: GeoMetricDetailOutput,
    override val askingRentPSF: GeoMetricDetailOutput?,
    override val effectiveRent: GeoMetricDetailOutput,
    override val effectiveRentPSF: GeoMetricDetailOutput?,
    override val recordsQuantity: Int,
    override val averageListingDays: BigDecimal,
    override val totalProperties: Int,
) : GeoMetricDataOutput

data class BedroomGeoMetricsOutput(
    override val id: String,
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    override val metrics: List<BedroomRentGeoMetricOutput>,
    override val type: AggregatedMetricType = AggregatedMetricType.BEDROOMS,
) : GeoRentMetricOutput

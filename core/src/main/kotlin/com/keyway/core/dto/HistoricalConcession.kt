package com.keyway.core.dto

import com.keyway.core.dto.historical.HistoricalDateRange
import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalConcessionSummary(
    val propertyId: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionRate: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class MsaHistoricalConcessionSummary(
    val msaCode: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionRate: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class ZipHistoricalConcessionSummary(
    val zipCode: String,
    override val avgConcessionValue: BigDecimal,
    override val avgConcessionRate: BigDecimal,
    override val values: List<HistoricalConcessionValue>,
) : HistoricalConcession

data class HistoricalConcessionValue(
    override val dateFrom: LocalDate,
    override val dateTo: LocalDate,
    val totalProperties: Int,
    val totalPropertiesWithConcession: Int,
    val avgConcessionValue: BigDecimal,
    val avgConcessionRate: BigDecimal,
) : HistoricalDateRange

sealed interface HistoricalConcession {
    val avgConcessionValue: BigDecimal
    val avgConcessionRate: BigDecimal
    val values: List<HistoricalConcessionValue>
}

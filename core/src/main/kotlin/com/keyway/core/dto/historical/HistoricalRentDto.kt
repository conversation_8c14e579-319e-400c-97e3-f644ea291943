package com.keyway.core.dto.historical

import com.keyway.core.dto.Metric
import java.math.BigDecimal
import java.time.LocalDate

data class HistoricalRentDto(
    val dateFrom: LocalDate,
    val dateTo: LocalDate,
    val averageRent: BigDecimal,
    val averageRentPSF: BigDecimal?,
    val medianRent: BigDecimal?,
    val medianRentPSF: BigDecimal?,
    val rent: Metric?,
    val rentSPF: Metric?,
)

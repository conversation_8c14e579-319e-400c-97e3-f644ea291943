package com.keyway.core.usecases.listings

import com.keyway.core.dto.listings.input.GetPropertiesListingsInput
import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.usecases.UseCase
import com.keyway.core.usecases.UseCaseAsync

class GetPropertiesLastListingUseCase(
    private val getPropertiesListingService: GetPropertiesListingService,
    private val listingsWithEffectiveRentMapper: ListingsWithEffectiveRentMapper,
) : UseCaseAsync<GetPropertiesListingsInput, Map<String, List<RentListingWithEffectiveRentOutput>>> {
    override suspend fun execute(input: GetPropertiesListingsInput): Map<String, List<RentListingWithEffectiveRentOutput>> =
        getPropertiesListingService
            .last(input)
            .ifEmpty { throw NotFoundException("No listings data for the given properties: ${input.propertyIds}") }
            .let(listingsWithEffectiveRentMapper::mapByProperty)
}

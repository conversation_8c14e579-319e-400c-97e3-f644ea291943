package com.keyway.core.usecases.metrics

import com.keyway.core.dto.Metric
import com.keyway.core.dto.MetricDto
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.input.ComputeAggregatedMetricInput
import com.keyway.core.dto.listings.output.BedroomMetricsOutput
import com.keyway.core.dto.listings.output.BedroomRentMetricOutput
import com.keyway.core.dto.listings.output.ByIdMetricsOutput
import com.keyway.core.dto.listings.output.ByIdRentMetricOutput
import com.keyway.core.dto.listings.output.FloorPlanMetricsOutput
import com.keyway.core.dto.listings.output.FloorPlanRentMetricOutput
import com.keyway.core.dto.listings.output.MetricDataOutput
import com.keyway.core.dto.listings.output.MetricDetailOutput
import com.keyway.core.dto.listings.output.RentMetricOutput
import com.keyway.core.dto.listings.output.UnitMixMetricsOutput
import com.keyway.core.dto.listings.output.UnitMixRentMetricsOutput
import com.keyway.core.dto.listings.output.UnitsMetricsOutput
import com.keyway.core.dto.listings.output.UnitsRentMetricOutput
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.distributeEvenly
import com.keyway.kommons.mapper.dataclass.mapTo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class ComputeAggregatedMetricUseCase(
    private val repository: MetricsRepository,
) : UseCaseAsync<ComputeAggregatedMetricInput, List<RentMetricOutput>> {
    override suspend fun execute(input: ComputeAggregatedMetricInput): List<RentMetricOutput> {
        val query = buildQuery(input)

        val partitionedIds = query.ids.distributeEvenly()

        val results =
            withContext(Dispatchers.IO) {
                val deferredResults =
                    partitionedIds.map { ids ->
                        async {
                            repository.aggregateMetrics(query.copy(ids = ids.toSet()))
                        }
                    }
                deferredResults.awaitAll().flatten()
            }

        return buildResult(
            query.dateFrom,
            query.dateTo,
            input.type,
            results,
        ).ifEmpty {
            throw NotFoundException("No metrics data for ids: ${input.ids}")
        }
    }

    private fun buildQuery(input: ComputeAggregatedMetricInput): MetricsFiltersQuery =
        MetricsFiltersQuery(
            ids = input.ids,
            idType = input.idType,
            dateFrom = input.dateFrom ?: DateUtils.getDefaultDateFrom(),
            dateTo = input.dateTo ?: DateUtils.getDefaultDateTo(),
            type = MetricType.valueOf(input.type.toString()),
            rentListingType = RentListingType.UNIT.takeIf { input.type == AggregatedMetricType.UNITS },
            unitCondition = input.unitCondition,
        )

    private fun buildResult(
        dateFrom: LocalDate,
        dateTo: LocalDate,
        type: AggregatedMetricType,
        results: List<MetricDto>,
    ): List<RentMetricOutput> =
        when (type) {
            AggregatedMetricType.UNIT_MIX ->
                mapMetrics(results, ::mapUnitMixMetric) { propertyId, metrics ->
                    UnitMixMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            AggregatedMetricType.FLOOR_PLAN ->
                mapMetrics(results, ::mapFloorPlanMetric) { propertyId, metrics ->
                    FloorPlanMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            AggregatedMetricType.BEDROOMS ->
                mapMetrics(results, ::mapBedroomMetric) { propertyId, metrics ->
                    BedroomMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            AggregatedMetricType.BY_ID ->
                mapMetrics(results, ::mapPropertyMetric) { propertyId, metrics ->
                    ByIdMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }

            AggregatedMetricType.UNITS ->
                mapMetrics(results, ::mapUnitsMetric) { propertyId, metrics ->
                    UnitsMetricsOutput(propertyId, dateFrom, dateTo, metrics)
                }
        }

    private fun <T : RentMetricOutput, U : MetricDataOutput> mapMetrics(
        results: List<MetricDto>,
        metricsMapper: (MetricDto) -> U,
        outputConstructor: (String, List<U>) -> T,
    ): List<T> =
        results.groupBy { it.id }.map { (propertyId, metrics) ->
            val mappedMetrics = metrics.map(metricsMapper)
            outputConstructor(propertyId, mappedMetrics)
        }

    private fun mapUnitMixMetric(metric: MetricDto): UnitMixRentMetricsOutput =
        UnitMixRentMetricsOutput(
            bedrooms = metric.bedrooms!!,
            bathrooms = metric.bathrooms!!,
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            deposit = metric.deposit?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays,
            unitsAvailable = metric.unitsAvailable,
            totalUnits = metric.totalUnits,
            squareFootage = metric.squareFootage?.mapTo(),
        )

    private fun mapFloorPlanMetric(metric: MetricDto): FloorPlanRentMetricOutput =
        FloorPlanRentMetricOutput(
            floorPlan = metric.floorPlan ?: "",
            bedrooms = metric.bedrooms!!,
            bathrooms = metric.bathrooms!!,
            squareFootage = metric.squareFootage?.mapTo(),
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            deposit = metric.deposit?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays,
            unitsAvailable = metric.unitsAvailable,
            totalUnits = metric.totalUnits,
        )

    private fun mapUnitsMetric(metric: MetricDto): UnitsRentMetricOutput =
        UnitsRentMetricOutput(
            floorPlan = metric.floorPlan ?: "",
            bedrooms = metric.bedrooms!!,
            bathrooms = metric.bathrooms!!,
            squareFootage = metric.squareFootage?.average,
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            deposit = metric.deposit?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays,
            unitsAvailable = metric.unitsAvailable,
            totalUnits = metric.totalUnits,
            unitId = metric.unitId!!,
        )

    private fun mapBedroomMetric(metric: MetricDto): BedroomRentMetricOutput =
        BedroomRentMetricOutput(
            bedrooms = metric.bedrooms!!,
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            deposit = metric.deposit?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays.setScale(2, RoundingMode.HALF_UP),
            unitsAvailable = metric.unitsAvailable,
            totalUnits = metric.totalUnits,
            squareFootage = metric.squareFootage?.mapTo(),
        )

    private fun mapPropertyMetric(metric: MetricDto): ByIdRentMetricOutput =
        ByIdRentMetricOutput(
            askingRent = metric.askingRent.mapTo(),
            askingRentPSF = metric.askingRent.perSquareFootage(metric.squareFootage?.average),
            effectiveRent = metric.effectiveRent.mapTo(),
            effectiveRentPSF = metric.effectiveRent.perSquareFootage(metric.squareFootage?.average),
            deposit = metric.deposit?.mapTo(),
            squareFootage = metric.squareFootage?.mapTo(),
            recordsQuantity = metric.totalRecords,
            averageListingDays = metric.averageListingsDays.setScale(2, RoundingMode.HALF_UP),
            unitsAvailable = metric.unitsAvailable,
            totalUnits = metric.totalUnits,
        )

    private fun Metric.perSquareFootage(squareFootage: BigDecimal?): MetricDetailOutput? =
        squareFootage.takeIf { it != null && it > BigDecimal.ZERO }?.let {
            MetricDetailOutput(
                min = this.min.divide(squareFootage, RoundingMode.HALF_UP),
                max = this.max.divide(squareFootage, RoundingMode.HALF_UP),
                average = this.average.divide(squareFootage, RoundingMode.HALF_UP),
                median = this.median.divide(squareFootage, RoundingMode.HALF_UP),
            )
        }
}

package com.keyway.core.usecases.historical

import com.keyway.core.dto.HistoricalExposure
import com.keyway.core.dto.HistoricalExposureSummary
import com.keyway.core.dto.HistoricalExposureValue
import com.keyway.core.dto.MsaHistoricalExposureSummary
import com.keyway.core.dto.ZipHistoricalExposureSummary
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.BigDecimalUtil.safeDiv
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode

class GetHistoricalExposureUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
    private val multifamilyPropertyRepository: MultifamilyPropertyRepository,
) : UseCaseAsync<
        HistoricalInput,
        HistoricalExposure,
    > {
    override suspend fun execute(input: HistoricalInput): HistoricalExposure =
        withContext(Dispatchers.IO) {
            val values =
                when (input.idType) {
                    IdType.PROPERTY -> getPropertyExposures(input)
                    else ->
                        historicalRentRepository.getExposureByZipOrMsa(
                            id = input.ids.first(),
                            idType = input.idType,
                            dateFrom = input.dateFrom,
                            dateTo = input.dateTo,
                            periodicity = input.periodicity,
                        )
                }.sortedBy { it.dateFrom }

            val avgRate = safeDiv(values.sumOf { it.avgExposureRate }, values.size.toBigDecimal()) ?: BigDecimal.ZERO

            when (input.idType) {
                IdType.PROPERTY ->
                    HistoricalExposureSummary(
                        propertyId = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
                IdType.ZIP_CODE ->
                    ZipHistoricalExposureSummary(
                        zipCode = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
                IdType.MSA ->
                    MsaHistoricalExposureSummary(
                        msaCode = input.ids.first(),
                        avgExposureRate = avgRate,
                        values = values,
                    )
            }
        }

    private suspend fun getPropertyExposures(input: HistoricalInput): List<HistoricalExposureValue> =
        withContext(Dispatchers.IO) {
            val propertyId = input.ids.first()
            val propertyUnits =
                multifamilyPropertyRepository.getByPropertyUnitsByIds(input.ids)[propertyId]?.takeIf { it > 1 }?.toBigDecimal()
                    ?: return@withContext emptyList()

            val askingDeferred =
                async {
                    historicalRentRepository.getHistoricalRents(
                        ids = setOf(propertyId),
                        idType = input.idType,
                        dateFrom = input.dateFrom,
                        dateTo = input.dateTo,
                        periodicity = input.periodicity,
                        rentType = RentType.ASKING,
                        bedrooms = null,
                        bathrooms = null,
                        unitCondition = null,
                    )
                }

            val asking = askingDeferred.await()

            asking.mapNotNull { value ->
                HistoricalExposureValue(
                    dateFrom = value.dateFrom,
                    dateTo = value.dateTo,
                    avgExposureRate = value.totalRecords.toBigDecimal().divide(propertyUnits, 3, RoundingMode.HALF_EVEN),
                ).takeIf { propertyUnits > value.totalRecords.toBigDecimal() }
            }
        }
}

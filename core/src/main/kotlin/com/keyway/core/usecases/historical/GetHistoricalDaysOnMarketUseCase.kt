package com.keyway.core.usecases.historical

import com.keyway.core.dto.historical.HistoricalDaysOnMarketInput
import com.keyway.core.dto.historical.HistoricalDaysOnMarketSummaryDto
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GetHistoricalDaysOnMarketUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<HistoricalDaysOnMarketInput, HistoricalDaysOnMarketSummaryDto> {
    override suspend fun execute(input: HistoricalDaysOnMarketInput): HistoricalDaysOnMarketSummaryDto =
        withContext(Dispatchers.IO) {
            val values =
                historicalRentRepository
                    .getDaysOnMarket(
                        id = input.historicalInput.ids.first(),
                        idType = input.historicalInput.idType,
                        dateFrom = input.historicalInput.dateFrom,
                        dateTo = input.historicalInput.dateTo,
                        periodicity = input.historicalInput.periodicity,
                        bedrooms = input.bedrooms,
                    ).sortedBy { it.dateTo }

            HistoricalDaysOnMarketSummaryDto(
                values = values,
            )
        }
}

package com.keyway.core.usecases.multifamily

import com.keyway.core.entities.MultifamilyProperty
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.core.usecases.UseCase

class SaveOrUpdateProperty(
    private val multifamilyPropertyRepository: MultifamilyPropertyRepository,
) : UseCase<MultifamilyProperty, Unit> {
    override fun execute(input: MultifamilyProperty) {
        multifamilyPropertyRepository.saveOrUpdate(input)
    }
}

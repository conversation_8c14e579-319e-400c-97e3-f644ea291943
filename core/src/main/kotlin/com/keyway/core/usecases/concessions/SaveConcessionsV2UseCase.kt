package com.keyway.core.usecases.concessions

import com.keyway.core.dto.concessions.input.ConcessionRecordV2Input
import com.keyway.core.dto.concessions.input.SaveConcessionV2Input
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.core.ports.repositories.TransactionRepository
import com.keyway.core.service.concession.SaveConcessionServiceV2
import com.keyway.core.usecases.UseCase
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.IdGenerator
import org.slf4j.LoggerFactory
import java.time.LocalDate

class SaveConcessionsV2UseCase(
    private val propertyConcessionRepository: PropertyConcessionV2Repository,
    private val transactionRepository: TransactionRepository,
    private val idGenerator: IdGenerator,
    private val saveConcessionService: SaveConcessionServiceV2,
) : UseCase<SaveConcessionV2Input, Unit> {
    private val logger = LoggerFactory.getLogger(this::class.java)

    companion object {
        const val CONCESSION_LOG_PREFIX = "[CONCESSION_DATA_ERROR]"
        const val IMPORT_PROCESS = "concessions_data"
        const val MAX_DAYS_PERIOD: Long = 7
    }

    override fun execute(input: SaveConcessionV2Input) {
        runCatching {
            val (dateFrom, dateTo) = getDatesFromRecords(input.records)
            val incomingConcessions = buildIncomingConcession(input)

            val existingConcessions =
                propertyConcessionRepository
                    .findByPropertyId(
                        input.propertyId,
                        dateFrom.minusDays(MAX_DAYS_PERIOD),
                        dateTo.plusDays(MAX_DAYS_PERIOD),
                    )

            val concessions = saveConcessionService.processConcessions(incomingConcessions, existingConcessions)

            persistConcessions(existingConcessions, concessions)
        }.onFailure {
            logger.warn(
                "$CONCESSION_LOG_PREFIX Error during concession event for property_id:${input.propertyId} " +
                    "caused by error:${it.message} - import_process:$IMPORT_PROCESS",
            )
        }
    }

    private fun buildIncomingConcession(input: SaveConcessionV2Input): List<PropertyConcessionV2> {
        val sortedRecords =
            input.records.sortedWith(compareBy { it.recordDate })

        if (sortedRecords.isEmpty()) return emptyList()

        val result = mutableListOf<PropertyConcessionV2>()
        var currentGroup = mutableListOf(sortedRecords.first())

        for (i in 1 until sortedRecords.size) {
            val currentRecord = sortedRecords[i]
            val previousRecord = sortedRecords[i - 1]

            val isOverlapping = previousRecord.recordDate.plusDays(1) >= currentRecord.recordDate
            val isSameText = previousRecord.concessionText == currentRecord.concessionText

            if (isOverlapping && isSameText) {
                currentGroup.add(currentRecord)
            } else {
                result.add(buildConcessionFromGroup(currentGroup, input.propertyId, input.zipCode, input.msaCode))
                currentGroup = mutableListOf(currentRecord)
            }
        }

        result.add(buildConcessionFromGroup(currentGroup, input.propertyId, input.zipCode, input.msaCode))

        return result.sortedBy { it.dateFrom }
    }

    private fun buildConcessionFromGroup(
        group: List<ConcessionRecordV2Input>,
        propertyId: String,
        zipCode: String?,
        msaCode: String?,
    ): PropertyConcessionV2 {
        val dateFrom = group.minOf { it.recordDate }
        val dateTo = group.maxOf { it.recordDate }
        return buildConcession(propertyId, zipCode, msaCode, dateFrom, dateTo, group.first())
    }

    private fun getDatesFromRecords(records: List<ConcessionRecordV2Input>): Pair<LocalDate, LocalDate> {
        require(records.isNotEmpty()) { "Records list cannot be empty" }
        val minDate = records.minOf { it.recordDate }
        val maxDate = records.maxOf { it.recordDate }
        return Pair(minDate, maxDate)
    }

    private fun buildConcession(
        propertyId: String,
        zipCode: String?,
        msaCode: String?,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        input: ConcessionRecordV2Input,
    ): PropertyConcessionV2 =
        PropertyConcessionV2(
            id = idGenerator.invoke(),
            propertyId = propertyId,
            concessionText = input.concessionText,
            benefits = input.benefits,
            dateFrom = dateFrom,
            dateTo = dateTo,
            createdAt = DateUtils.now(),
            updatedAt = DateUtils.now(),
            zipCode = zipCode,
            msaCode = msaCode,
        )

    private fun persistConcessions(
        existingConcessions: List<PropertyConcessionV2>,
        concessions: List<PropertyConcessionV2>,
    ) {
        logger.debug("Existing {}", existingConcessions)

        val concessionsToDelete =
            existingConcessions.filter { existing ->
                concessions.none { it.id == existing.id }
            }

        transactionRepository.executeTransaction {
            if (concessionsToDelete.isNotEmpty()) {
                logger.debug("To delete {}", concessionsToDelete)
                propertyConcessionRepository.delete(concessionsToDelete.map { it.id })
            }

            if (concessions.isNotEmpty()) {
                logger.debug("To persist {}", concessions)
                concessions.forEach { concession ->
                    propertyConcessionRepository.saveOrUpdate(concession)
                }
            }
        }
    }
}

package com.keyway.core.usecases.listings

import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.BedroomFutureAvailability
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.UseCaseAsync
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

class GetZipAndMsaFutureAvailabilityUseCase(
    private val listingsRepository: ListingsRepository,
) : UseCaseAsync<
        GetZipAndMsaFutureAvailabilityUseCase.Input,
        List<BedroomFutureAvailability>,
    > {
    override suspend fun execute(input: Input): List<BedroomFutureAvailability> =
        withContext(Dispatchers.IO) {
            input.idType.takeIf { it != IdType.PROPERTY }?.let {
                listingsRepository.zipAndMsaFutureAvailabilityByBedroom(input.id, input.idType)
            } ?: emptyList()
        }

    data class Input(
        val id: String,
        val idType: IdType,
    )
}

package com.keyway.core.usecases.historical

import com.keyway.core.dto.HistoricalConcession
import com.keyway.core.dto.HistoricalConcessionSummary
import com.keyway.core.dto.HistoricalConcessionValue
import com.keyway.core.dto.MsaHistoricalConcessionSummary
import com.keyway.core.dto.ZipHistoricalConcessionSummary
import com.keyway.core.dto.historical.HistoricalInput
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.RentType
import com.keyway.core.ports.repositories.HistoricalRentRepository
import com.keyway.core.usecases.UseCaseAsync
import com.keyway.core.utils.BigDecimalUtil.safeDiv
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import java.math.BigDecimal
import java.math.RoundingMode

class GetHistoricalConcessionByIdsUseCase(
    private val historicalRentRepository: HistoricalRentRepository,
) : UseCaseAsync<
        HistoricalInput,
        HistoricalConcession,
    > {
    override suspend fun execute(input: HistoricalInput): HistoricalConcession =
        withContext(Dispatchers.IO) {
            val values =
                when (input.idType) {
                    IdType.PROPERTY -> getPropertyConcessions(input)
                    else -> historicalRentRepository.getConcessionByZipOrMsa(input.ids.first(), input.idType, input.dateFrom, input.dateTo, input.periodicity)
                }.sortedBy { it.dateFrom }

            val avgConcessionValue = safeDiv(values.sumOf { it.avgConcessionValue }, values.size.toBigDecimal()) ?: BigDecimal.ZERO
            val avgConcessionRate = safeDiv(values.sumOf { it.avgConcessionRate }, values.size.toBigDecimal()) ?: BigDecimal.ZERO

            when (input.idType) {
                IdType.PROPERTY ->
                    HistoricalConcessionSummary(
                        propertyId = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
                IdType.ZIP_CODE ->
                    ZipHistoricalConcessionSummary(
                        zipCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
                IdType.MSA ->
                    MsaHistoricalConcessionSummary(
                        msaCode = input.ids.first(),
                        avgConcessionValue = avgConcessionValue,
                        avgConcessionRate = avgConcessionRate,
                        values = values,
                    )
            }
        }

    private suspend fun CoroutineScope.getPropertyConcessions(input: HistoricalInput): List<HistoricalConcessionValue> {
        val askingDeferred =
            async {
                historicalRentRepository.getHistoricalRents(
                    ids = input.ids,
                    idType = input.idType,
                    dateFrom = input.dateFrom,
                    dateTo = input.dateTo,
                    periodicity = input.periodicity,
                    rentType = RentType.ASKING,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        val effectiveDeferred =
            async {
                historicalRentRepository.getHistoricalRents(
                    ids = input.ids,
                    idType = input.idType,
                    dateFrom = input.dateFrom,
                    dateTo = input.dateTo,
                    periodicity = input.periodicity,
                    rentType = RentType.EFFECTIVE,
                    bedrooms = null,
                    bathrooms = null,
                    unitCondition = null,
                )
            }

        val (askingValues, effectiveValues) = awaitAll(askingDeferred, effectiveDeferred)
        val effectiveByProperty = effectiveValues.associateBy { it.getKey() }
        val values =
            askingValues.map { asking ->
                val askingRent = asking.totalRent.divide(asking.totalRecords.toBigDecimal(), 2, RoundingMode.HALF_UP)
                val effectiveRent =
                    effectiveByProperty[asking.getKey()]?.let {
                        it.totalRent.divide(it.totalRecords.toBigDecimal(), 2, RoundingMode.HALF_UP)
                    } ?: askingRent

                HistoricalConcessionValue(
                    dateFrom = asking.dateFrom,
                    dateTo = asking.dateTo,
                    totalProperties = 1,
                    totalPropertiesWithConcession = 1.takeIf { askingRent > effectiveRent } ?: 0,
                    avgConcessionValue = askingRent - effectiveRent,
                    avgConcessionRate = safeDiv(askingRent - effectiveRent, askingRent) ?: BigDecimal.ZERO,
                )
            }
        return values
    }

    private fun HistoricalRentOutput.getKey() = "${this.id}-${this.dateFrom}-${this.dateTo}"
}

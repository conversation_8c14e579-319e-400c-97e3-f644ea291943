package com.keyway.core.service.concession

import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.entities.conciliation.ConciliationConcessionV2
import com.keyway.core.utils.DateUtils
import java.time.LocalDate
import java.time.OffsetDateTime

class SaveConcessionServiceV2 {
    fun processConcessions(
        incomingConcessions: List<PropertyConcessionV2>,
        existingConcessions: List<PropertyConcessionV2>,
    ): List<PropertyConcessionV2> {
        val existingConcessionsMap =
            existingConcessions
                .flatMap { concession ->
                    DateUtils.generateDateRange(concession.dateFrom, concession.dateTo).map { date ->
                        date to createConciliationObject(date, concession)
                    }
                }.toMap()

        val incomingConcessionsMap =
            incomingConcessions
                .sortedBy { it.dateFrom }
                .flatMap { concession ->
                    DateUtils.generateDateRange(concession.dateFrom, concession.dateTo).map { date ->
                        date to createConciliationObject(date, concession)
                    }
                }.toMap()

        val (lowerLimit, higherLimit) =
            calculateSequenceLimits(
                incomingConcessions.sortedBy { it.dateFrom },
                existingConcessions.sortedBy { it.dateFrom },
            )

        val concessionsMap = conciliateConcessions(lowerLimit, higherLimit, incomingConcessionsMap, existingConcessionsMap)

        return generateConcessions(concessionsMap)
    }

    private fun calculateSequenceLimits(
        incomingConcessions: List<PropertyConcessionV2>,
        existingConcessions: List<PropertyConcessionV2>,
    ): Pair<LocalDate, LocalDate> =
        listOfNotNull(
            existingConcessions.firstOrNull()?.dateFrom,
            existingConcessions.lastOrNull()?.dateTo,
            incomingConcessions.first().dateFrom,
            incomingConcessions.last().dateTo,
        ).let { it.min() to it.max() }

    private fun createConciliationObject(
        date: LocalDate,
        concession: PropertyConcessionV2,
    ): ConciliationConcessionV2 =
        ConciliationConcessionV2(
            id = concession.id,
            propertyId = concession.propertyId,
            concessionText = concession.concessionText,
            benefits = concession.benefits,
            date = date,
            createdAt = concession.createdAt,
            updatedAt = concession.updatedAt,
            zipCode = concession.zipCode,
            msaCode = concession.msaCode,
        )

    private fun generateConcessions(concessionsMap: Map<LocalDate, ConciliationConcessionV2>): List<PropertyConcessionV2> =
        concessionsMap.values.groupBy { it.id }.map { (_, concessions) ->
            val concession = concessions.first()
            PropertyConcessionV2(
                id = concession.id,
                propertyId = concession.propertyId,
                concessionText = concession.concessionText,
                benefits = concessions.last().benefits,
                dateFrom = concession.date,
                dateTo = concessions.maxBy { it.date }.date,
                createdAt = concession.createdAt,
                updatedAt = concession.updatedAt,
                zipCode = concessions.last().zipCode,
                msaCode = concessions.last().msaCode,
            )
        }

    private fun conciliateConcessions(
        lowerLimit: LocalDate,
        higherLimit: LocalDate,
        incomingConcessionsMap: Map<LocalDate, ConciliationConcessionV2>,
        existingConcessionsMap: Map<LocalDate, ConciliationConcessionV2>,
    ): Map<LocalDate, ConciliationConcessionV2> {
        val conciliatedListingsMap = mutableMapOf<LocalDate, ConciliationConcessionV2>()
        var lastConcessionProcessed: ConciliationConcessionV2? = null
        DateUtils
            .generateDateRange(lowerLimit, higherLimit)
            .forEach { date ->
                val existingConcession = existingConcessionsMap[date]
                val incomingConcession = incomingConcessionsMap[date]

                val conciliatedConcession = selectConcession(existingConcession, incomingConcession, lastConcessionProcessed)

                if (conciliatedConcession != null) {
                    conciliatedListingsMap[date] = conciliatedConcession
                    lastConcessionProcessed = conciliatedConcession
                }
            }
        return conciliatedListingsMap
    }

    fun applyConsistency(
        concession: ConciliationConcessionV2,
        lastConcessionProcessed: ConciliationConcessionV2?,
    ): ConciliationConcessionV2 =
        concession.copy(
            id = getConcessionId(concession, lastConcessionProcessed),
            createdAt = getCreatedAt(concession, lastConcessionProcessed),
            benefits = concession.benefits,
            zipCode = concession.zipCode,
            msaCode = concession.msaCode,
        )

    private fun selectConcession(
        existingConcession: ConciliationConcessionV2?,
        incomingConcession: ConciliationConcessionV2?,
        lastConcessionProcessed: ConciliationConcessionV2?,
    ): ConciliationConcessionV2? =
        when {
            existingConcession != null && incomingConcession != null -> {
                if (existingConcession.concessionText != incomingConcession.concessionText) {
                    applyConsistency(incomingConcession, lastConcessionProcessed)
                } else {
                    createConcession(existingConcession, incomingConcession, lastConcessionProcessed)
                }
            }
            incomingConcession != null -> applyConsistency(incomingConcession, lastConcessionProcessed)
            existingConcession != null -> applyConsistency(existingConcession, lastConcessionProcessed)
            else -> null
        }

    private fun createConcession(
        existingConcession: ConciliationConcessionV2,
        incomingConcession: ConciliationConcessionV2,
        lastConcessionProcessed: ConciliationConcessionV2?,
    ): ConciliationConcessionV2 =
        when {
            lastConcessionProcessed?.isEquivalentTo(incomingConcession) == true ->
                incomingConcession.copy(
                    id = getConcessionId(incomingConcession, lastConcessionProcessed),
                )
            !existingConcession.isEquivalentTo(incomingConcession) ->
                incomingConcession.copy(
                    id = getConcessionId(incomingConcession, lastConcessionProcessed),
                )
            else ->
                existingConcession.copy(
                    benefits = incomingConcession.benefits,
                    zipCode = incomingConcession.zipCode,
                    msaCode = incomingConcession.msaCode,
                )
        }

    private fun getConcessionId(
        concession: ConciliationConcessionV2,
        lastConcessionProcessed: ConciliationConcessionV2?,
    ): String =
        when {
            lastConcessionProcessed == null -> concession.id
            lastConcessionProcessed.isEquivalentTo(concession) -> lastConcessionProcessed.id
            else -> concession.id
        }

    private fun getCreatedAt(
        concession: ConciliationConcessionV2,
        lastConcessionProcessed: ConciliationConcessionV2?,
    ): OffsetDateTime =
        when {
            lastConcessionProcessed == null -> concession.createdAt
            lastConcessionProcessed.isEquivalentTo(concession) -> lastConcessionProcessed.createdAt
            else -> concession.createdAt
        }
}

package com.keyway.core.utils

import java.math.BigDecimal

fun <T> Collection<T>.distributeEvenly(maxItemsPerPartition: Int = 5): List<List<T>> {
    require(maxItemsPerPartition > 0) { "Max items per partition must be positive" }
    val values = this.toList()

    // Handle empty collection
    if (isEmpty()) return listOf(emptyList())

    return when (val partitions = (size + maxItemsPerPartition - 1) / maxItemsPerPartition) {
        1 -> listOf(values)
        else -> {
            val itemsPerPartition = size / partitions
            val remainder = size % partitions

            chunked(itemsPerPartition + if (remainder > 0) 1 else 0)
                .take(partitions)
        }
    }
}

fun <T> Collection<T>.distributeEvenlyInMaxPartitions(maxPartitions: Int = 6): List<List<T>> {
    require(maxPartitions > 0) { "Max partitions must be positive" }

    val items = this.toList()

    // Handle edge cases
    if (isEmpty()) return listOf(emptyList())
    if (size == 1) return listOf(items)

    // Determine actual number of partitions to create
    val actualPartitions = minOf(maxPartitions, size)

    // Calculate how items should be distributed
    val baseItemsPerPartition = size / actualPartitions
    val extraItems = size % actualPartitions

    // Build partitions by taking slices from the original list
    return buildPartitions(items, actualPartitions, baseItemsPerPartition, extraItems)
}

private fun <T> buildPartitions(
    items: List<T>,
    partitionCount: Int,
    baseSize: Int,
    extraItems: Int,
): List<List<T>> {
    val partitions = mutableListOf<List<T>>()
    var startIndex = 0

    repeat(partitionCount) { partitionIndex ->
        val partitionSize = baseSize + if (shouldGetExtraItem(partitionIndex, extraItems)) 1 else 0
        val endIndex = startIndex + partitionSize

        partitions.add(items.subList(startIndex, endIndex))
        startIndex = endIndex
    }

    return partitions
}

private fun shouldGetExtraItem(
    partitionIndex: Int,
    extraItems: Int,
): Boolean = partitionIndex < extraItems

fun List<BigDecimal>.average(): Double =
    if (isEmpty()) {
        0.0
    } else {
        map { it.toDouble() }.average()
    }

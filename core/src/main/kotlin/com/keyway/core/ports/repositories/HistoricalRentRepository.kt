package com.keyway.core.ports.repositories

import com.keyway.core.dto.HistoricalConcessionValue
import com.keyway.core.dto.HistoricalExposureValue
import com.keyway.core.dto.historical.HistoricalDaysOnMarketDto
import com.keyway.core.dto.historical.HistoricalRentOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.entities.HistoricalPeriodicity
import com.keyway.core.entities.RentType
import com.keyway.core.entities.UnitCondition
import java.math.BigDecimal
import java.time.LocalDate

interface HistoricalRentRepository {
    suspend fun getHistoricalRents(
        ids: Set<String>,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        rentType: RentType,
        bedrooms: Int?,
        bathrooms: BigDecimal?,
        unitCondition: UnitCondition?,
    ): List<HistoricalRentOutput>

    suspend fun getExposureByZipOrMsa(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): List<HistoricalExposureValue>

    suspend fun getConcessionByZipOrMsa(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
    ): List<HistoricalConcessionValue>

    suspend fun getDaysOnMarket(
        id: String,
        idType: IdType,
        dateFrom: LocalDate,
        dateTo: LocalDate,
        periodicity: HistoricalPeriodicity,
        bedrooms: Int?,
    ): List<HistoricalDaysOnMarketDto>
}

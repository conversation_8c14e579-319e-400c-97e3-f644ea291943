package application.health

import application.utils.base.BaseApplicationTest
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.Unirest
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class HealthCheckTest : BaseApplicationTest() {
    @Test
    fun `Health check test`() {
        // Given
        val givenUrl = "${localUrl()}/health"

        // When
        val result = Unirest.get(givenUrl).asString()

        // Then
        assertEquals(JsonMapper.decode(result.body, Map::class.java)["status"], "ok")
    }
}

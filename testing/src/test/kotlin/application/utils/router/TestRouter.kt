package application.utils.router

import com.keyway.adapters.exceptions.BadRequestException
import com.keyway.application.router.Router
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get

class TestRouter : Router {
    override fun setUpRoutes(routing: Routing) {
        routing.get("/test/exception/bad-request") {
            throw BadRequestException()
        }

        routing.get("/test/exception/runtime") {
            throw RuntimeException()
        }
    }
}

package application.utils.idgenerator

import com.keyway.core.utils.IdGenerator
import java.util.LinkedList
import java.util.Queue
import java.util.UUID

class FixedIdGenerator : IdGenerator {
    private val idQueue: Queue<String> = LinkedList()

    fun setId(mockedId: String) {
        idQueue.add(mockedId)
    }

    fun cleanQueue() = idQueue.clear()

    override fun invoke(): String =
        when (idQueue.isNotEmpty()) {
            true -> idQueue.poll()
            false -> UUID.randomUUID().toString()
        }
}

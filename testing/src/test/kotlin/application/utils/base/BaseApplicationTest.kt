package application.utils.base

import application.utils.idgenerator.FixedIdGenerator
import application.utils.module.TestModule
import application.utils.stub.StubMessagePublisher
import com.keyway.adapters.repositories.utils.ViewsUtils
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.SystemConfig
import com.keyway.application.configuration.parser.ConfigParser
import com.keyway.application.koin.ModuleConstants
import com.keyway.application.koin.ModuleConstants.ROUTES
import com.keyway.application.koin.modules.loader.ModuleLoader
import com.keyway.application.koin.starter.KoinStarter
import com.keyway.application.ktor.KtorApp.createApp
import com.keyway.application.mapper.AppMapperConfigs.lowerCamelCaseObjectMapper
import com.keyway.application.router.Router
import com.keyway.core.entities.MultifamilyProperty
import com.keyway.core.ports.repositories.MultifamilyPropertyRepository
import com.keyway.core.service.MessageBatchPublisher
import com.keyway.kommons.db.transaction.datasource.TransactionalDataSource
import com.keyway.kommons.sqs.SqsConsumer
import io.ktor.server.engine.EmbeddedServer
import io.ktor.server.netty.NettyApplicationEngine
import kong.unirest.Unirest
import kong.unirest.jackson.JacksonObjectMapper
import org.flywaydb.core.Flyway
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach
import org.koin.core.component.KoinComponent
import org.koin.core.component.inject
import org.koin.core.qualifier.named
import org.koin.java.KoinJavaComponent
import org.koin.test.KoinTest
import org.koin.test.inject
import utils.SqsHelpers.start
import java.io.File
import java.sql.Statement

abstract class BaseApplicationTest : KoinTest {
    private val systemConfig: SystemConfig by inject()

    protected fun localUrl() = "http://localhost:${systemConfig.httpPort}"

    companion object : KoinComponent {
        private var isInitialized = false
        val dataSource: TransactionalDataSource by inject()
        val consumers: Set<SqsConsumer> by inject(named(ModuleConstants.CONSUMERS))
        private lateinit var app: EmbeddedServer<NettyApplicationEngine, NettyApplicationEngine.Configuration>
        private val configuration: Configuration by inject()
        private val routes: Set<Router> by inject(named(ROUTES))
        val idGenerator: FixedIdGenerator by inject()
        val messagePublisher: MessageBatchPublisher by inject()
        private val multifamilyPropertyRepository: MultifamilyPropertyRepository by inject()

        @JvmStatic
        @BeforeAll
        fun init() {
            if (isInitialized.not()) {
                Unirest.config().objectMapper = JacksonObjectMapper(lowerCamelCaseObjectMapper)
                ConfigParser.read(isTest = true)
                ModuleLoader.modules.add(TestModule.modules)
                KoinStarter.start()
                app = createApp(configuration, routes).also { it.start() }
                isInitialized = true
                migrateDB()
                consumers.start()
            }
        }

        private fun migrateDB() {
            Flyway
                .configure()
                .dataSource(KoinJavaComponent.get(TransactionalDataSource::class.java))
                .locations("/db/migration")
                .load()
                .migrate()
        }
    }

    @BeforeEach
    fun clearDB() {
        idGenerator.cleanQueue()
        (messagePublisher as StubMessagePublisher).clean()
        File(javaClass.classLoader.getResource("db/clear_db_data.sql").file)
            .readLines()
            .filter { it.isNotBlank() }
            .forEach { delete ->
                dataSource.connection.use { conn ->
                    conn.createStatement().use { st ->
                        st.execute(delete)
                    }
                }
            }
    }

    fun createAndRefreshMaterializedViews() {
        executeRawSql { statement ->
            // Create monthly rent views
            statement.execute("call create_monthly_rent_views()")
            // Refresh rent listing materialized views
            statement.execute("call refresh_materialized_views('${ViewsUtils.MaterializedViews.RENT_LISTING.prefix}%')")
            // Refresh effective rent materialized views
            statement.execute("call refresh_materialized_views('${ViewsUtils.MaterializedViews.EFFECTIVE_RENT.prefix}%')")
            // Refresh effective rent materialized views
            statement.execute("call refresh_materialized_views('${ViewsUtils.MaterializedViews.SUMMARY.prefix}%')")
            // Refresh effective rent materialized views
            statement.execute("call refresh_materialized_views('${ViewsUtils.MaterializedViews.BED_SUMMARY.prefix}%')")
        }
    }

    fun createProperty(property: MultifamilyProperty) {
        multifamilyPropertyRepository.saveOrUpdate(property)
    }

    fun executeRawSql(block: (statement: Statement) -> Unit) {
        dataSource.connection.use { connection ->
            connection.createStatement().use { statement ->
                block(statement)
            }
        }
    }
}

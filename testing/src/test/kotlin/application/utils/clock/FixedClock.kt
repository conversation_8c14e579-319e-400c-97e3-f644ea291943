package application.utils.clock

import java.time.Clock
import java.time.Instant
import java.time.ZoneId

class FixedClock(
    private var fixedInstant: Instant,
    private var zoneId: ZoneId,
) : Clock() {
    override fun instant(): Instant = fixedInstant

    override fun withZone(zone: ZoneId): Clock = FixedClock(fixedInstant, zone)

    override fun getZone(): ZoneId = zoneId

    fun setFixedInstant(fixedInstant: Instant) {
        this.fixedInstant = fixedInstant
    }
}

package application.error

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.error.response.RestExceptionResponse
import com.keyway.kommons.mapper.JsonMapper
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ErrorHandlerTest : BaseApplicationTest() {
    @Test
    fun `When fail flow with an rest exception should be manage by <PERSON>rror<PERSON><PERSON><PERSON>`() {
        // Given
        val givenUrl = "${localUrl()}/test/exception/bad-request"

        // When
        val result = Unirest.get(givenUrl).asString()

        // Then
        val response = JsonMapper.decode(result.body, RestExceptionResponse::class.java)
        assertEquals(response.errorCode, "BAD_REQUEST")
        assertEquals(response.message, "400 | BAD_REQUEST - Bad Request")
        assertEquals(response.statusCode, 400)
    }

    @Test
    fun `When fail flow with an exception should be manage by <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>`() {
        // Given
        val givenUrl = "${localUrl()}/test/exception/runtime"

        // When
        val result = Unirest.get(givenUrl).asString()

        // Then
        val response = JsonMapper.decode(result.body, RestExceptionResponse::class.java)
        assertEquals(response.errorCode, "INTERNAL_SERVER_ERROR")
        assertEquals(response.message, "500 | INTERNAL_SERVER_ERROR - Internal Server Error")
        assertEquals(response.statusCode, 500)
    }
}

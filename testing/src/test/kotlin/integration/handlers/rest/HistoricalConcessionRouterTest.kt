package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.core.dto.HistoricalConcessionSummary
import com.keyway.core.dto.MsaHistoricalConcessionSummary
import com.keyway.core.dto.ZipHistoricalConcessionSummary
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.MockedEntityFactory.buildMultifamilyProperty
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class HistoricalConcessionRouterTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    private val testZipCode = "33009"
    private val testMsaCode = "35620"
    private val testPropertyId = "USFL-014695"
    private val testPropertyId2 = "USFL-014696"

    @BeforeEach
    fun setUp() {
        createProperty(buildMultifamilyProperty(testPropertyId))
        createProperty(buildMultifamilyProperty(testPropertyId2))

        var date = LocalDate.now().minusYears(1)
        for (x in 0..400) {
            getRentListing(
                rent = Money.of("1000") + Money.of(x),
                dateTo = date,
                dateFrom = date,
            ).let { listing ->

                listingsRepository.save(listing).let {
                    effectiveRentRepository.save(
                        EffectiveRent.build(
                            id = UUID.randomUUID().toString(),
                            rent = listing.rent,
                            rentDeposit = listing.rentDeposit,
                            dateTo = listing.dateTo,
                            dateFrom = listing.dateFrom,
                            listing = listing,
                            concessionIds = emptyList(),
                            concessions = "",
                            createdAt = listing.createdAt,
                            updateAt = listing.updateAt,
                        ),
                    )
                }
            }

            getRentListing(
                propertyId = testPropertyId2,
                rent = Money.of("1200") + Money.of(x),
                dateTo = date,
                dateFrom = date,
            ).let { listing ->
                listingsRepository.save(listing).let {
                    effectiveRentRepository.save(
                        EffectiveRent.build(
                            id = UUID.randomUUID().toString(),
                            rent = listing.rent * Money.of(0.9), // Lower effective rent to create concession
                            rentDeposit = listing.rentDeposit,
                            dateTo = listing.dateTo,
                            dateFrom = listing.dateFrom,
                            listing = listing,
                            concessionIds = emptyList(),
                            concessions = "",
                            createdAt = listing.createdAt,
                            updateAt = listing.updateAt,
                        ),
                    )
                }
            }
            date = date.plusDays(1)
        }

        createAndRefreshMaterializedViews()
    }

    @Test
    fun `should get property historical concession summary with default parameters`() {
        // Given
        val givenUrl = "${localUrl()}/multifamily/$testPropertyId/historical-concession-summary"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(HistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testPropertyId, summary.propertyId)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        assertTrue(summary.values.isNotEmpty())
    }

    @Test
    fun `should get ZIP code historical concession summary with default parameters`() {
        // Given
        val givenUrl = "${localUrl()}/zip-codes/$testZipCode/historical-concession-summary"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(ZipHistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testZipCode, summary.zipCode)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        assertTrue(summary.values.isNotEmpty())
    }

    @Test
    fun `should get MSA code historical concession summary with default parameters`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/historical-concession-summary"

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(MsaHistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testMsaCode, summary.msaCode)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        assertTrue(summary.values.isNotEmpty())
    }

    @Test
    fun `should get property historical concession summary with custom date range`() {
        // Given
        val givenUrl = "${localUrl()}/multifamily/$testPropertyId/historical-concession-summary"
        val dateFrom = LocalDate.now().minusDays(10)
        val dateTo = LocalDate.now()

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to dateFrom,
                        DATE_TO_PARAM_NAME to dateTo,
                    ),
                ).asObject(HistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testPropertyId, summary.propertyId)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        // Values might be empty for short date range, but structure should be valid
        assertNotNull(summary.values)
    }

    @Test
    fun `should get ZIP code historical concession summary with custom date range`() {
        // Given
        val givenUrl = "${localUrl()}/zip-codes/$testZipCode/historical-concession-summary"
        val dateFrom = LocalDate.now().minusDays(10)
        val dateTo = LocalDate.now()

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to dateFrom,
                        DATE_TO_PARAM_NAME to dateTo,
                    ),
                ).asObject(ZipHistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testZipCode, summary.zipCode)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        assertNotNull(summary.values)
    }

    @Test
    fun `should get MSA code historical concession summary with custom date range`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/historical-concession-summary"
        val dateFrom = LocalDate.now().minusDays(10)
        val dateTo = LocalDate.now()

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to dateFrom,
                        DATE_TO_PARAM_NAME to dateTo,
                    ),
                ).asObject(MsaHistoricalConcessionSummary::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        assertNotNull(result.body)

        val summary = result.body
        assertEquals(testMsaCode, summary.msaCode)
        assertNotNull(summary.avgConcessionValue)
        assertNotNull(summary.avgConcessionRate)
        assertNotNull(summary.values)
    }

    private fun getRentListing(
        propertyId: String = testPropertyId,
        rent: Money = Money.of("1000"),
        dateFrom: LocalDate = LocalDate.now(),
        dateTo: LocalDate = LocalDate.now(),
        bedroomsQuantity: Int = 2,
        bathroomsQuantity: BigDecimal = BigDecimal("1"),
    ) = RentListing(
        id = UUID.randomUUID().toString(),
        propertyId = propertyId,
        type = RentListingType.UNIT,
        typeId = "9-305",
        rent = rent,
        dateFrom = dateFrom,
        dateTo = dateTo,
        recordSource = "apartments",
        zipCode = testZipCode,
        msaCode = testMsaCode,
        unitSquareFootage = BigDecimal("1119"),
        bedroomsQuantity = bedroomsQuantity,
        bathroomsQuantity = bathroomsQuantity,
        floorPlan = "A2",
        availableIn = null,
        rentDeposit = Money.of("600"),
        createdAt = DateUtils.now(),
        updateAt = DateUtils.now(),
    )
}

package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.concessions.Benefit
import com.keyway.adapters.dtos.concessions.Concession
import com.keyway.application.utils.router.ParamUtils.ACTIVE_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import io.kotest.matchers.collections.shouldContainAll
import kong.unirest.GenericType
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

class GetPropertiesConcessionsV2Test : BaseApplicationTest() {
    private val propertyConcessionV2Repository: PropertyConcessionV2Repository by inject()

    companion object {
        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2024-07-08T00:00:00Z"))
    }

    private val concessionsV2Data =
        listOf(
            PropertyConcessionV2(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-030865",
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            freeMonthsUntil = null,
                            oneTimeDollarsOffAmount = null,
                            oneTimeDollarsOffPercentage = null,
                            recurringDollarsOffAmount = null,
                            recurringDollarsOffPercentage = BigDecimal("5.00"),
                            recurringMonthsTerm = 10,
                            leaseTermMonths = null,
                            conditionDeadline = LocalDate.parse("2024-06-30"),
                            conditionBedrooms = listOf(1, 2),
                            conditionUnitNames = null,
                            conditionFloorplans = null,
                            conditionSelectedUnits = null,
                            conditionSelectedFloorplans = null,
                            conditionSelectedEmployees = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            ),
            PropertyConcessionV2(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-030865",
                dateFrom = LocalDate.parse("2024-07-01"),
                dateTo = LocalDate.parse("2024-07-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            freeMonthsUntil = null,
                            oneTimeDollarsOffAmount = null,
                            oneTimeDollarsOffPercentage = null,
                            recurringDollarsOffAmount = null,
                            recurringDollarsOffPercentage = BigDecimal("15.00"),
                            recurringMonthsTerm = 10,
                            leaseTermMonths = null,
                            conditionDeadline = LocalDate.parse("2024-07-31"),
                            conditionBedrooms = null,
                            conditionUnitNames = null,
                            conditionFloorplans = null,
                            conditionSelectedUnits = null,
                            conditionSelectedFloorplans = null,
                            conditionSelectedEmployees = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-07-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-07-01T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            ),
            PropertyConcessionV2(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            freeMonthsUntil = null,
                            oneTimeDollarsOffAmount = null,
                            oneTimeDollarsOffPercentage = null,
                            recurringDollarsOffAmount = null,
                            recurringDollarsOffPercentage = BigDecimal("5.00"),
                            recurringMonthsTerm = 10,
                            leaseTermMonths = null,
                            conditionDeadline = LocalDate.parse("2024-06-30"),
                            conditionBedrooms = null,
                            conditionUnitNames = null,
                            conditionFloorplans = null,
                            conditionSelectedUnits = null,
                            conditionSelectedFloorplans = null,
                            conditionSelectedEmployees = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            ),
        )

    @Test
    fun `should return all concessions for properties without active filter`() {
        // Given
        val propertiesList = "USTX-030865,USTX-027626"
        val givenUrl = "${localUrl()}/v2/multifamily/concessions"

        concessionsV2Data.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            listOf(
                Concession(
                    propertyId = "USTX-030865",
                    dateFrom = LocalDate.parse("2024-06-01"),
                    dateTo = LocalDate.parse("2024-06-01"),
                    description = "15% discount on rent for the first 6 months",
                    active = false,
                    benefits =
                        listOf(
                            Benefit(
                                freeMonthsAmount = null,
                                freeMonthsUntil = null,
                                oneTimeDollarsOffAmount = null,
                                oneTimeDollarsOffPercentage = null,
                                recurringDollarsOffAmount = null,
                                recurringDollarsOffPercentage = BigDecimal("5.00"),
                                recurringMonthsTerm = 10,
                                leaseTermMonths = null,
                                conditionDeadline = LocalDate.parse("2024-06-30"),
                                conditionBedrooms = listOf(1, 2),
                                conditionUnitNames = null,
                                conditionFloorplans = null,
                                conditionSelectedUnits = null,
                                conditionSelectedFloorplans = null,
                                conditionSelectedEmployees = null,
                                waivedApplicationFee = null,
                                waivedSecurityDeposit = null,
                                waivedAdministrativeFee = null,
                                waivedMoveInFee = null,
                                cheaperSecurityDeposit = null,
                                cheaperAdministrativeFee = null,
                                cheaperApplicationFee = null,
                                cheaperMoveInFee = null,
                                cheaperRent = null,
                                benefitGroupId = null,
                            ),
                        ),
                ),
                Concession(
                    propertyId = "USTX-030865",
                    dateFrom = LocalDate.parse("2024-07-01"),
                    dateTo = LocalDate.parse("2024-07-01"),
                    description = "15% discount on rent for the first 6 months",
                    active = false,
                    benefits =
                        listOf(
                            Benefit(
                                freeMonthsAmount = null,
                                freeMonthsUntil = null,
                                oneTimeDollarsOffAmount = null,
                                oneTimeDollarsOffPercentage = null,
                                recurringDollarsOffAmount = null,
                                recurringDollarsOffPercentage = BigDecimal("15.00"),
                                recurringMonthsTerm = 10,
                                leaseTermMonths = null,
                                conditionDeadline = LocalDate.parse("2024-07-31"),
                                conditionBedrooms = null,
                                conditionUnitNames = null,
                                conditionFloorplans = null,
                                conditionSelectedUnits = null,
                                conditionSelectedFloorplans = null,
                                conditionSelectedEmployees = null,
                                waivedApplicationFee = null,
                                waivedSecurityDeposit = null,
                                waivedAdministrativeFee = null,
                                waivedMoveInFee = null,
                                cheaperSecurityDeposit = null,
                                cheaperAdministrativeFee = null,
                                cheaperApplicationFee = null,
                                cheaperMoveInFee = null,
                                cheaperRent = null,
                                benefitGroupId = null,
                            ),
                        ),
                ),
                Concession(
                    propertyId = "USTX-027626",
                    dateFrom = LocalDate.parse("2024-06-01"),
                    dateTo = LocalDate.parse("2024-06-01"),
                    description = "15% discount on rent for the first 6 months",
                    active = false,
                    benefits =
                        listOf(
                            Benefit(
                                freeMonthsAmount = null,
                                freeMonthsUntil = null,
                                oneTimeDollarsOffAmount = null,
                                oneTimeDollarsOffPercentage = null,
                                recurringDollarsOffAmount = null,
                                recurringDollarsOffPercentage = BigDecimal("5.00"),
                                recurringMonthsTerm = 10,
                                leaseTermMonths = null,
                                conditionDeadline = LocalDate.parse("2024-06-30"),
                                conditionBedrooms = null,
                                conditionUnitNames = null,
                                conditionFloorplans = null,
                                conditionSelectedUnits = null,
                                conditionSelectedFloorplans = null,
                                conditionSelectedEmployees = null,
                                waivedApplicationFee = null,
                                waivedSecurityDeposit = null,
                                waivedAdministrativeFee = null,
                                waivedMoveInFee = null,
                                cheaperSecurityDeposit = null,
                                cheaperAdministrativeFee = null,
                                cheaperApplicationFee = null,
                                cheaperMoveInFee = null,
                                cheaperRent = null,
                                benefitGroupId = null,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to LocalDate.parse("2024-05-01"),
                        DATE_TO_PARAM_NAME to LocalDate.parse("2024-08-31"),
                    ),
                ).asObject(object : GenericType<List<Concession>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should return the most recent concessions for properties`() {
        DateHelper.setClockUTC(Instant.parse("2024-07-01T00:00:00Z"))
        // Given
        val propertiesList = "USTX-030865,USTX-027626"
        val givenUrl = "${localUrl()}/v2/multifamily/concessions"

        concessionsV2Data.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            listOf(
                Concession(
                    propertyId = "USTX-030865",
                    dateFrom = LocalDate.parse("2024-07-01"),
                    dateTo = LocalDate.parse("2024-07-01"),
                    description = "15% discount on rent for the first 6 months",
                    active = true,
                    benefits =
                        listOf(
                            Benefit(
                                freeMonthsAmount = null,
                                freeMonthsUntil = null,
                                oneTimeDollarsOffAmount = null,
                                oneTimeDollarsOffPercentage = null,
                                recurringDollarsOffAmount = null,
                                recurringDollarsOffPercentage = BigDecimal("15.00"),
                                recurringMonthsTerm = 10,
                                leaseTermMonths = null,
                                conditionDeadline = LocalDate.parse("2024-07-31"),
                                conditionBedrooms = null,
                                conditionUnitNames = null,
                                conditionFloorplans = null,
                                conditionSelectedUnits = null,
                                conditionSelectedFloorplans = null,
                                conditionSelectedEmployees = null,
                                waivedApplicationFee = null,
                                waivedSecurityDeposit = null,
                                waivedAdministrativeFee = null,
                                waivedMoveInFee = null,
                                cheaperSecurityDeposit = null,
                                cheaperAdministrativeFee = null,
                                cheaperApplicationFee = null,
                                cheaperMoveInFee = null,
                                cheaperRent = null,
                                benefitGroupId = null,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        ACTIVE_PARAM_NAME to true,
                        DATE_FROM_PARAM_NAME to LocalDate.parse("2024-01-01"),
                        DATE_TO_PARAM_NAME to LocalDate.now(),
                    ),
                ).asObject(object : GenericType<List<Concession>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should return empty concessions list for nonexistent properties`() {
        // Given
        val propertiesList = "USTX-111111,USTX-022222"
        val givenUrl = "${localUrl()}/v2/multifamily/concessions"

        concessionsV2Data.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse = emptyList<Concession>()

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to LocalDate.parse("2024-05-01"),
                        DATE_TO_PARAM_NAME to LocalDate.parse("2024-08-31"),
                    ),
                ).asObject(object : GenericType<List<Concession>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
    }

    @Test
    fun `should return only the concession with max dateTo as active when multiple concessions are active`() {
        // Set the current date to 2024-07-10 for this test
        DateHelper.setClockUTC(Instant.parse("2024-07-10T00:00:00Z"))

        // Create a property ID for this test
        val propertyId = "USTX-040404"
        val givenUrl = "${localUrl()}/v2/multifamily/concessions"

        // Create two concessions that would both be considered active by shouldBeActive()
        // The shouldBeActive() method considers a concession active if the current date is between
        // dateFrom and dateTo + 3 days

        // First concession: earlier dates but still active
        val concession1 =
            PropertyConcessionV2(
                id = "concession-1",
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-07-05"),
                dateTo = LocalDate.parse("2024-07-08"), // With ACTIVE_AMOUNT_DAYS=3, this is active until 2024-07-11
                concessionText = "10% discount on rent for the first 3 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            freeMonthsUntil = null,
                            oneTimeDollarsOffAmount = null,
                            oneTimeDollarsOffPercentage = null,
                            recurringDollarsOffAmount = null,
                            recurringDollarsOffPercentage = BigDecimal("10.00"),
                            recurringMonthsTerm = 3,
                            leaseTermMonths = null,
                            conditionDeadline = LocalDate.parse("2024-08-31"),
                            conditionBedrooms = listOf(1, 2),
                            conditionUnitNames = null,
                            conditionFloorplans = null,
                            conditionSelectedUnits = null,
                            conditionSelectedFloorplans = null,
                            conditionSelectedEmployees = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-07-05T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-07-05T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            )

        // Second concession: later dateTo, should be the only one marked as active
        val concession2 =
            PropertyConcessionV2(
                id = "concession-2",
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-07-07"),
                dateTo = LocalDate.parse("2024-07-12"), // With ACTIVE_AMOUNT_DAYS=3, this is active until 2024-07-15
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            freeMonthsUntil = null,
                            oneTimeDollarsOffAmount = null,
                            oneTimeDollarsOffPercentage = null,
                            recurringDollarsOffAmount = null,
                            recurringDollarsOffPercentage = BigDecimal("15.00"),
                            recurringMonthsTerm = 6,
                            leaseTermMonths = null,
                            conditionDeadline = LocalDate.parse("2024-09-30"),
                            conditionBedrooms = listOf(1, 2, 3),
                            conditionUnitNames = null,
                            conditionFloorplans = null,
                            conditionSelectedUnits = null,
                            conditionSelectedFloorplans = null,
                            conditionSelectedEmployees = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-07-07T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-07-07T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            )

        // Save both concessions
        listOf(concession1, concession2).forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        // Make the API call with active=true to get only active concessions
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertyId,
                        ACTIVE_PARAM_NAME to true,
                        DATE_FROM_PARAM_NAME to LocalDate.parse("2024-07-01"),
                        DATE_TO_PARAM_NAME to LocalDate.parse("2024-07-31"),
                    ),
                ).asObject(object : GenericType<List<Concession>>() {})

        // Verify the response
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)

        // Should only return one concession (the one with the latest dateTo)
        assertEquals(1, result.body.size)

        // The returned concession should be the one with the latest dateTo
        val returnedConcession = result.body.first()
        assertEquals(concession2.propertyId, returnedConcession.propertyId)
        assertEquals(concession2.dateFrom, returnedConcession.dateFrom)
        assertEquals(concession2.dateTo, returnedConcession.dateTo)
        assertEquals(concession2.concessionText, returnedConcession.description)
        assertTrue(returnedConcession.active)

        // Reset the clock for other tests
        DateHelper.setClockUTC(Instant.parse("2024-07-08T00:00:00Z"))
    }
}

package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.metrics.MetricDetail
import com.keyway.adapters.dtos.metrics.property.BedroomRentMetric
import com.keyway.adapters.dtos.metrics.property.BedroomRentSummary
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentMetric
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentSummary
import com.keyway.adapters.dtos.metrics.property.PropertyRentMetric
import com.keyway.adapters.dtos.metrics.property.PropertyRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitMixRentMetric
import com.keyway.adapters.dtos.metrics.property.UnitMixRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitsRentSummary
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import integration.handlers.utils.DataUtils
import io.kotest.matchers.collections.shouldContainAll
import kong.unirest.GenericType
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class GetPropertyMetricsHandlerTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    @BeforeEach
    fun setUp() {
        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))
        DataUtils.realisticCaseUnitsData.forEach {
            listingsRepository.save(it)
            effectiveRentRepository.save(
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = it,
                    concessionIds = emptyList(),
                    dateTo = it.dateTo,
                    dateFrom = it.dateFrom,
                    concessions = "THIS IS ONE",
                    rentDeposit = it.rentDeposit,
                    rent = it.rent,
                    createdAt = it.createdAt,
                    updateAt = it.updateAt,
                ),
            )
        }
    }

    @Test
    fun `should handle property metrics request`() {
        val metricType = "property"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2024-01-01"))

        val expectedResponse =
            listOf(
                PropertyRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            PropertyRentMetric(
                                askingRent =
                                    MetricDetail(
                                        min = BigDecimal("1000.00"),
                                        max = BigDecimal("1300.00"),
                                        average = BigDecimal("1133.33"),
                                        median = BigDecimal("1125.00"),
                                    ),
                                askingRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("2.07"),
                                        max = BigDecimal("2.69"),
                                        average = BigDecimal("2.34"),
                                        median = BigDecimal("2.33"),
                                    ),
                                effectiveRent =
                                    MetricDetail(
                                        min = BigDecimal("1000.00"),
                                        max = BigDecimal("1300.00"),
                                        average = BigDecimal("1133.33"),
                                        median = BigDecimal("1125.00"),
                                    ),
                                effectiveRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("2.07"),
                                        max = BigDecimal("2.69"),
                                        average = BigDecimal("2.34"),
                                        median = BigDecimal("2.33"),
                                    ),
                                deposit =
                                    MetricDetail(
                                        min = BigDecimal("200.00"),
                                        max = BigDecimal("800.00"),
                                        average = BigDecimal("575.00"),
                                        median = BigDecimal("650.00"),
                                    ),
                                squareFootage =
                                    MetricDetail(
                                        min = BigDecimal("400.00"),
                                        max = BigDecimal("550.00"),
                                        average = BigDecimal("483.33"),
                                        median = BigDecimal("500.00"),
                                    ),
                                averageListingDays = BigDecimal("4.67"),
                                recordsQuantity = 6,
                                unitsAvailable = 0,
                                totalUnits = 3,
                            ),
                        ),
                ),
                PropertyRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            PropertyRentMetric(
                                askingRent =
                                    MetricDetail(
                                        min = BigDecimal("1093.00"),
                                        max = BigDecimal("1093.00"),
                                        average = BigDecimal("1093.00"),
                                        median = BigDecimal("1093.00"),
                                    ),
                                askingRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("1.22"),
                                        max = BigDecimal("1.22"),
                                        average = BigDecimal("1.22"),
                                        median = BigDecimal("1.22"),
                                    ),
                                effectiveRent =
                                    MetricDetail(
                                        min = BigDecimal("1093.00"),
                                        max = BigDecimal("1093.00"),
                                        average = BigDecimal("1093.00"),
                                        median = BigDecimal("1093.00"),
                                    ),
                                effectiveRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("1.22"),
                                        max = BigDecimal("1.22"),
                                        average = BigDecimal("1.22"),
                                        median = BigDecimal("1.22"),
                                    ),
                                deposit = null,
                                squareFootage =
                                    MetricDetail(
                                        min = BigDecimal("895.00"),
                                        max = BigDecimal("895.00"),
                                        average = BigDecimal("895.00"),
                                        median = BigDecimal("895.00"),
                                    ),
                                averageListingDays = BigDecimal("1.00"),
                                recordsQuantity = 1,
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<PropertyRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle property metrics request with units available`() {
        val metricType = "property"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2023-10-17"))

        val expectedResponse =
            listOf(
                PropertyRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            PropertyRentMetric(
                                askingRent =
                                    MetricDetail(
                                        min = BigDecimal("1000.00"),
                                        max = BigDecimal("1200.00"),
                                        average = BigDecimal("1100.00"),
                                        median = BigDecimal("1100.00"),
                                    ),
                                askingRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("2.13"),
                                        max = BigDecimal("2.55"),
                                        average = BigDecimal("2.34"),
                                        median = BigDecimal("2.34"),
                                    ),
                                effectiveRent =
                                    MetricDetail(
                                        min = BigDecimal("1000.00"),
                                        max = BigDecimal("1200.00"),
                                        average = BigDecimal("1100.00"),
                                        median = BigDecimal("1100.00"),
                                    ),
                                effectiveRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("2.13"),
                                        max = BigDecimal("2.55"),
                                        average = BigDecimal("2.34"),
                                        median = BigDecimal("2.34"),
                                    ),
                                deposit =
                                    MetricDetail(
                                        min = BigDecimal("200.00"),
                                        max = BigDecimal("700.00"),
                                        average = BigDecimal("500.00"),
                                        median = BigDecimal("600.00"),
                                    ),
                                squareFootage =
                                    MetricDetail(
                                        min = BigDecimal("400.00"),
                                        max = BigDecimal("550.00"),
                                        average = BigDecimal("470.00"),
                                        median = BigDecimal("450.00"),
                                    ),
                                averageListingDays = BigDecimal("2.60"),
                                recordsQuantity = 5,
                                unitsAvailable = 2,
                                totalUnits = 3,
                            ),
                        ),
                ),
                PropertyRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            PropertyRentMetric(
                                askingRent =
                                    MetricDetail(
                                        min = BigDecimal("1093.00"),
                                        max = BigDecimal("1093.00"),
                                        average = BigDecimal("1093.00"),
                                        median = BigDecimal("1093.00"),
                                    ),
                                askingRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("1.22"),
                                        max = BigDecimal("1.22"),
                                        average = BigDecimal("1.22"),
                                        median = BigDecimal("1.22"),
                                    ),
                                effectiveRent =
                                    MetricDetail(
                                        min = BigDecimal("1093.00"),
                                        max = BigDecimal("1093.00"),
                                        average = BigDecimal("1093.00"),
                                        median = BigDecimal("1093.00"),
                                    ),
                                effectiveRentPSF =
                                    MetricDetail(
                                        min = BigDecimal("1.22"),
                                        max = BigDecimal("1.22"),
                                        average = BigDecimal("1.22"),
                                        median = BigDecimal("1.22"),
                                    ),
                                deposit = null,
                                squareFootage =
                                    MetricDetail(
                                        min = BigDecimal("895.00"),
                                        max = BigDecimal("895.00"),
                                        average = BigDecimal("895.00"),
                                        median = BigDecimal("895.00"),
                                    ),
                                averageListingDays = BigDecimal("0.00"),
                                recordsQuantity = 1,
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<PropertyRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        //  result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle unit mix metrics request`() {
        val metricType = "unit-mix"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2024-01-01"))

        val expectedResponse =
            listOf(
                UnitMixRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            UnitMixRentMetric(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("450.00"), average = BigDecimal("416.67"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("0.33"),
                                unitsAvailable = 0,
                                totalUnits = 2,
                            ),
                            UnitMixRentMetric(
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                squareFootage = MetricDetail(min = BigDecimal("550.00"), max = BigDecimal("550.00"), average = BigDecimal("550.00"), median = BigDecimal("550.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1216.67"), median = BigDecimal("1200.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.21"), median = BigDecimal("2.18")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1216.67"), median = BigDecimal("1200.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.21"), median = BigDecimal("2.18")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("800.00"), average = BigDecimal("566.67"), median = BigDecimal("700.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("7.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
                UnitMixRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            UnitMixRentMetric(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("895.00"), max = BigDecimal("895.00"), average = BigDecimal("895.00"), median = BigDecimal("895.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<UnitMixRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        //  result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle unit mix metrics request with units available`() {
        val metricType = "unit-mix"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2023-10-17"))

        val expectedResponse =
            listOf(
                UnitMixRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            UnitMixRentMetric(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("450.00"), average = BigDecimal("416.67"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("0.33"),
                                unitsAvailable = 1,
                                totalUnits = 2,
                            ),
                            UnitMixRentMetric(
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                squareFootage = MetricDetail(min = BigDecimal("550.00"), max = BigDecimal("550.00"), average = BigDecimal("550.00"), median = BigDecimal("550.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("700.00"), average = BigDecimal("450.00"), median = BigDecimal("450.00")),
                                recordsQuantity = 2,
                                averageListingDays = BigDecimal("6.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
                UnitMixRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            UnitMixRentMetric(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("895.00"), max = BigDecimal("895.00"), average = BigDecimal("895.00"), median = BigDecimal("895.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<UnitMixRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        //  result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle bedrooms metrics request`() {
        val metricType = "bedrooms"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2024-01-01"))

        val expectedResponse =
            listOf(
                BedroomRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            BedroomRentMetric(
                                bedrooms = 2,
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("450.00"), average = BigDecimal("416.67"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("0.33"),
                                unitsAvailable = 0,
                                totalUnits = 2,
                            ),
                            BedroomRentMetric(
                                bedrooms = 3,
                                squareFootage = MetricDetail(min = BigDecimal("550.00"), max = BigDecimal("550.00"), average = BigDecimal("550.00"), median = BigDecimal("550.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1216.67"), median = BigDecimal("1200.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.21"), median = BigDecimal("2.18")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1216.67"), median = BigDecimal("1200.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.21"), median = BigDecimal("2.18")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("800.00"), average = BigDecimal("566.67"), median = BigDecimal("700.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("7.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
                BedroomRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            BedroomRentMetric(
                                bedrooms = 2,
                                squareFootage = MetricDetail(min = BigDecimal("895.00"), max = BigDecimal("895.00"), average = BigDecimal("895.00"), median = BigDecimal("895.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<BedroomRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle bedrooms metrics request with units available`() {
        val metricType = "bedrooms"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2023-10-17"))

        val expectedResponse =
            listOf(
                BedroomRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            BedroomRentMetric(
                                bedrooms = 2,
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("450.00"), average = BigDecimal("416.67"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.40"), max = BigDecimal("2.64"), average = BigDecimal("2.52"), median = BigDecimal("2.52")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("0.33"),
                                unitsAvailable = 1,
                                totalUnits = 2,
                            ),
                            BedroomRentMetric(
                                bedrooms = 3,
                                squareFootage = MetricDetail(min = BigDecimal("550.00"), max = BigDecimal("550.00"), average = BigDecimal("550.00"), median = BigDecimal("550.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("700.00"), average = BigDecimal("450.00"), median = BigDecimal("450.00")),
                                recordsQuantity = 2,
                                averageListingDays = BigDecimal("6.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
                BedroomRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            BedroomRentMetric(
                                bedrooms = 2,
                                squareFootage = MetricDetail(min = BigDecimal("895.00"), max = BigDecimal("895.00"), average = BigDecimal("895.00"), median = BigDecimal("895.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<BedroomRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
// result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle floor plan metrics request`() {
        val metricType = "floor-plan"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2024-01-01"))

        val expectedResponse =
            listOf(
                FloorPlanRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            FloorPlanRentMetric(
                                floorPlan = "A2",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("400.00"), average = BigDecimal("400.00"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.50"), max = BigDecimal("2.75"), average = BigDecimal("2.63"), median = BigDecimal("2.63")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.50"), max = BigDecimal("2.75"), average = BigDecimal("2.63"), median = BigDecimal("2.63")),
                                deposit = null,
                                recordsQuantity = 2,
                                averageListingDays = BigDecimal("1.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                            FloorPlanRentMetric(
                                floorPlan = "A21",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("400.00"), average = BigDecimal("400.00"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1050.00"), max = BigDecimal("1050.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.33"), max = BigDecimal("2.33"), average = BigDecimal("2.33"), median = BigDecimal("2.33")),
                                effectiveRent = MetricDetail(min = BigDecimal("1050.00"), max = BigDecimal("1050.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.33"), max = BigDecimal("2.33"), average = BigDecimal("2.33"), median = BigDecimal("2.33")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("2.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                            FloorPlanRentMetric(
                                floorPlan = "A3",
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("400.00"), average = BigDecimal("400.00"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1220.83"), median = BigDecimal("1200.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.22"), median = BigDecimal("2.18")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1300.00"), average = BigDecimal("1229.83"), median = BigDecimal("1200.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.36"), average = BigDecimal("2.22"), median = BigDecimal("2.18")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("800.00"), average = BigDecimal("566.67"), median = BigDecimal("700.00")),
                                recordsQuantity = 3,
                                averageListingDays = BigDecimal("8.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
                FloorPlanRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            FloorPlanRentMetric(
                                floorPlan = "A2",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("400.00"), average = BigDecimal("400.00"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("1.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<FloorPlanRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
// result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle floor plan metrics request with units available`() {
        val metricType = "floor-plan"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2023-10-17"))

        val expectedResponse =
            listOf(
                FloorPlanRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            FloorPlanRentMetric(
                                floorPlan = "A2",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("400.00"), max = BigDecimal("400.00"), average = BigDecimal("400.00"), median = BigDecimal("400.00")),
                                askingRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.50"), max = BigDecimal("2.75"), average = BigDecimal("2.63"), median = BigDecimal("2.63")),
                                effectiveRent = MetricDetail(min = BigDecimal("1000.00"), max = BigDecimal("1100.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.50"), max = BigDecimal("2.75"), average = BigDecimal("2.63"), median = BigDecimal("2.63")),
                                deposit = null,
                                recordsQuantity = 2,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                            FloorPlanRentMetric(
                                floorPlan = "A21",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("450.00"), max = BigDecimal("450.00"), average = BigDecimal("450.00"), median = BigDecimal("450.00")),
                                askingRent = MetricDetail(min = BigDecimal("1050.00"), max = BigDecimal("1050.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.33"), max = BigDecimal("2.33"), average = BigDecimal("2.33"), median = BigDecimal("2.33")),
                                effectiveRent = MetricDetail(min = BigDecimal("1050.00"), max = BigDecimal("1050.00"), average = BigDecimal("1050.00"), median = BigDecimal("1050.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.33"), max = BigDecimal("2.33"), average = BigDecimal("2.33"), median = BigDecimal("2.33")),
                                deposit = MetricDetail(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00")),
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("1.00"),
                                unitsAvailable = 0,
                                totalUnits = 1,
                            ),
                            FloorPlanRentMetric(
                                floorPlan = "A3",
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                squareFootage = MetricDetail(min = BigDecimal("550.00"), max = BigDecimal("550.00"), average = BigDecimal("550.00"), median = BigDecimal("550.00")),
                                askingRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                effectiveRent = MetricDetail(min = BigDecimal("1150.00"), max = BigDecimal("1200.00"), average = BigDecimal("1175.00"), median = BigDecimal("1175.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("2.09"), max = BigDecimal("2.18"), average = BigDecimal("2.14"), median = BigDecimal("2.14")),
                                deposit = MetricDetail(min = BigDecimal("200.00"), max = BigDecimal("700.00"), average = BigDecimal("450.00"), median = BigDecimal("450.00")),
                                recordsQuantity = 2,
                                averageListingDays = BigDecimal("6.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
                FloorPlanRentSummary(
                    propertyId = "USTX-022222",
                    dateFrom = dates.first,
                    dateTo = dates.second,
                    metrics =
                        listOf(
                            FloorPlanRentMetric(
                                floorPlan = "A2",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = MetricDetail(min = BigDecimal("895.00"), max = BigDecimal("895.00"), average = BigDecimal("895.00"), median = BigDecimal("895.00")),
                                askingRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                askingRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                effectiveRent = MetricDetail(min = BigDecimal("1093.00"), max = BigDecimal("1093.00"), average = BigDecimal("1093.00"), median = BigDecimal("1093.00")),
                                effectiveRentPSF = MetricDetail(min = BigDecimal("1.22"), max = BigDecimal("1.22"), average = BigDecimal("1.22"), median = BigDecimal("1.22")),
                                deposit = null,
                                recordsQuantity = 1,
                                averageListingDays = BigDecimal("0.00"),
                                unitsAvailable = 1,
                                totalUnits = 1,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<FloorPlanRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // result.body.shouldContainAll(expectedResponse)
    }

    @Test
    fun `should handle units metrics request`() {
        val metricType = "units"
        val propertiesList = "USTX-027626,USTX-022222"
        val dates = Pair(LocalDate.parse("2023-01-01"), LocalDate.parse("2023-10-17"))
        val result =
            Unirest
                .get(getGivenUrl(metricType))
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(object : GenericType<List<UnitsRentSummary>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
    }

    private fun getGivenUrl(metricType: String) = "${localUrl()}/multifamily/metrics/$metricType"
}

package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.UnitRentSuggestions
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.utils.DateUtils
import kong.unirest.Unirest
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class GetRentSuggestionHandlerTest : BaseApplicationTest() {
    private val propertyUnitRepository: PropertyUnitRepository by inject()
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    private val basePropertyId = "USFL-111111"
    private val compProperty1 = "USFL-222222"
    private val compProperty2 = "USFL-333333"

    companion object {
        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2023-10-20T00:00:00Z"))
    }

    @Test
    fun `calculate suggested rent`() {
        val mockedUnits = mockedUnits()
        mockedUnits.forEach { unit -> propertyUnitRepository.saveOrUpdate(unit) }

        val compsUnits =
            mockedUnits.filter { unit ->
                unit.propertyId in setOf(compProperty1, compProperty2)
            }

        val rentListings =
            compsUnits.map { unit ->
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = unit.propertyId,
                    type = RentListingType.UNIT,
                    typeId = unit.unitId,
                    rent = Money.of(1091 * unit.bedrooms),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = unit.bedrooms,
                    bathroomsQuantity = unit.bathrooms,
                    floorPlan = unit.floorPlan,
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                )
            }

        val effectiveRents =
            rentListings.map {
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = it,
                    concessionIds = listOf(),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    rent = Money.of(500 * it.bedroomsQuantity),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = OffsetDateTime.now(),
                    updateAt = OffsetDateTime.now(),
                    isActive = true,
                )
            }

        listingsRepository.save(rentListings)
        effectiveRentRepository.save(effectiveRents)
        val propertyIds = "$compProperty1,$compProperty2"
        val dates = Pair(LocalDate.parse("2023-08-01"), LocalDate.parse("2023-11-01"))

        val givenUrl = "${localUrl()}/multifamily/$basePropertyId/unit-rent-suggestions"

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertyIds,
                        DATE_FROM_PARAM_NAME to dates.first,
                        DATE_TO_PARAM_NAME to dates.second,
                    ),
                ).asObject(UnitRentSuggestions::class.java)

        assertEquals(result.status, 200)
        assertNotNull(result.body)
    }

    private fun mockedUnits() =
        listOf(
            PropertyUnit(
                unitId = "100",
                propertyId = basePropertyId,
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "200",
                propertyId = basePropertyId,
                squareFootage = BigDecimal("300"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            // EQUALS
            PropertyUnit(
                unitId = "1",
                propertyId = compProperty1,
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            // LESS SQFT SAME UNIT MIX
            PropertyUnit(
                unitId = "11",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            // DIFFERENT RENO STATE
            PropertyUnit(
                unitId = "12",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = true,
                renovationProbability = BigDecimal("0.99"),
                amenities = setOf("Pool"),
            ),
            // BIGGER IN UNIT MIX AND SQFT
            PropertyUnit(
                unitId = "2",
                propertyId = compProperty1,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            // BIGGER IN UNIT MIX BUT EQUALS IN SQFT
            PropertyUnit(
                unitId = "22",
                propertyId = compProperty1,
                squareFootage = BigDecimal("100"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            PropertyUnit(
                unitId = "3",
                propertyId = compProperty1,
                squareFootage = BigDecimal("300"),
                bedrooms = 3,
                bathrooms = BigDecimal("2.5"),
                floorPlan = "F3",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            ),
            PropertyUnit(
                unitId = "201",
                propertyId = compProperty2,
                squareFootage = BigDecimal("300"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "202",
                propertyId = compProperty2,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "203",
                propertyId = compProperty2,
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = true,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "204",
                propertyId = compProperty2,
                squareFootage = BigDecimal("450"),
                bedrooms = 2,
                bathrooms = BigDecimal(2),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
            PropertyUnit(
                unitId = "205",
                propertyId = compProperty2,
                squareFootage = BigDecimal("300"),
                bedrooms = 3,
                bathrooms = BigDecimal(3),
                floorPlan = "Fx2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            ),
        )
}

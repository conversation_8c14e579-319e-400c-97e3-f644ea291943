package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.metrics.historical.HistoricalRentSummary
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.core.dto.listings.input.SaveRentGroupedListingInput
import com.keyway.core.dto.listings.input.UnitRecordsData
import com.keyway.core.entities.RentListingType
import com.keyway.core.usecases.listings.SaveGroupedListingUseCase
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class GetHistoricalRentTest : BaseApplicationTest() {
    private val saveGroupedListingUseCase: SaveGroupedListingUseCase by inject()

    private fun saveListing(
        propertyId: String = UUID.randomUUID().toString(),
        unitId: String = UUID.randomUUID().toString(),
        unitSquareFootage: BigDecimal = BigDecimal("100"),
        bedroomsQuantity: Int = 2,
        bathroomsQuantity: BigDecimal = BigDecimal("1"),
        rent: BigDecimal = BigDecimal("2335"),
        rentDeposit: BigDecimal = BigDecimal("600.0"),
        recordDate: LocalDate = LocalDate.now(),
        effectiveRent: BigDecimal = BigDecimal("2335"),
    ) = SaveRentGroupedListingInput(
        propertyId = propertyId,
        type = RentListingType.UNIT,
        typeId = unitId,
        zipCode = "33009",
        msaCode = "35620",
        bedroomsQuantity = bedroomsQuantity,
        bathroomsQuantity = bathroomsQuantity,
        floorPlan = "floorplan",
        unitSquareFootage = unitSquareFootage,
        records =
            listOf(
                UnitRecordsData(
                    recordSource = "apartments",
                    rent = rent,
                    rentDeposit = rentDeposit,
                    effectiveRent = effectiveRent,
                    effectiveRentDeposit = null,
                    availableIn = null,
                    recordDate = recordDate,
                    concessions = "",
                ),
            ),
    ).also {
        saveGroupedListingUseCase.execute(it)
    }

    private fun getGivenUrl(propertyId: String) = "${localUrl()}/multifamily/$propertyId/historical-rent-summary"

    @Test
    fun `should get daily rent history`() {
        val initialDate = LocalDate.now().minusDays(14)
        val initial =
            saveListing(
                propertyId = "UUSS-012315",
                recordDate = initialDate,
                rent = BigDecimal("1000"),
                effectiveRent = BigDecimal("1000"),
            )
        val finalDate = LocalDate.now()
        val final =
            saveListing(
                propertyId = initial.propertyId,
                unitId = initial.typeId,
                recordDate = finalDate,
                rent = BigDecimal("1100"),
                effectiveRent = BigDecimal("1100"),
            )

        val result =
            Unirest
                .get(getGivenUrl(initial.propertyId))
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to initialDate,
                        DATE_TO_PARAM_NAME to finalDate,
                    ),
                ).asObject(HistoricalRentSummary::class.java)

        Assertions.assertEquals(HttpStatus.OK, result.status)
        Assertions.assertEquals(2, result.body.historicalRents.size)
        Assertions.assertEquals(BigDecimal("0.10"), result.body.rentChange)
    }

    @Test
    fun `should get daily rent with change diff between beds history`() {
        val initialDate = LocalDate.now().minusDays(14)
        val initial =
            saveListing(
                propertyId = "UUSS-012315",
                recordDate = initialDate,
                rent = BigDecimal("1000"),
                effectiveRent = BigDecimal("1000"),
            )
        saveListing(
            propertyId = initial.propertyId,
            recordDate = initialDate,
            rent = BigDecimal("10000"),
            bedroomsQuantity = 5,
            unitSquareFootage = BigDecimal("2000"),
            effectiveRent = BigDecimal("10000"),
        )
        val finalDate = LocalDate.now()
        val final =
            saveListing(
                propertyId = initial.propertyId,
                unitId = initial.typeId,
                recordDate = finalDate,
                rent = BigDecimal("1100"),
                effectiveRent = BigDecimal("1100"),
            )
        saveListing(
            propertyId = initial.propertyId,
            recordDate = finalDate,
            rent = BigDecimal("20000"),
            bedroomsQuantity = 5,
            unitSquareFootage = BigDecimal("2000"),
            effectiveRent = BigDecimal("20000"),
        )

        val result =
            Unirest
                .get(getGivenUrl(initial.propertyId))
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to initialDate,
                        DATE_TO_PARAM_NAME to finalDate,
                    ),
                ).asObject(HistoricalRentSummary::class.java)

        Assertions.assertEquals(HttpStatus.OK, result.status)
        Assertions.assertEquals(2, result.body.historicalRents.size)
        Assertions.assertEquals(BigDecimal("0.92"), result.body.rentChange)
    }

    @Test
    fun `should get min max rent with change diff between beds history`() {
        val initialDate = LocalDate.now().minusDays(14)
        val initial =
            saveListing(
                propertyId = "UUSS-012315",
                recordDate = initialDate,
                rent = BigDecimal("1000"),
                effectiveRent = BigDecimal("1000"),
            )
        saveListing(
            propertyId = initial.propertyId,
            recordDate = initialDate,
            rent = BigDecimal("10000"),
            bedroomsQuantity = 5,
            unitSquareFootage = BigDecimal("2000"),
            effectiveRent = BigDecimal("10000"),
        )
        val finalDate = LocalDate.now()

        val result =
            Unirest
                .get(getGivenUrl(initial.propertyId))
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to initialDate,
                        DATE_TO_PARAM_NAME to finalDate,
                    ),
                ).asObject(HistoricalRentSummary::class.java)

        Assertions.assertEquals(HttpStatus.OK, result.status)
        Assertions.assertEquals(1, result.body.historicalRents.size)
        Assertions.assertEquals(
            BigDecimal("10000.00"),
            result.body.historicalRents
                .get(0)
                .rent.max,
        )
        Assertions.assertEquals(
            BigDecimal("1000.00"),
            result.body.historicalRents
                .get(0)
                .rent.min,
        )
    }
}

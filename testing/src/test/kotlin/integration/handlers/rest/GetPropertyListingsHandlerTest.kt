package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.application.utils.router.ParamUtils.BATHROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.LISTING_TYPE_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.UNIT_ID_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import integration.handlers.utils.DataUtils
import io.kotest.matchers.collections.shouldContainAll
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID
import kotlin.collections.map

class GetPropertyListingsHandlerTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    fun createEffective(listing: RentListing) =
        EffectiveRent(
            id = UUID.randomUUID().toString(),
            rentListingId = listing.id,
            concessionIds = listOf(),
            dateFrom = listing.dateFrom,
            dateTo = listing.dateTo,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            concessions = "",
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
            propertyId = listing.propertyId,
            type = listing.type,
            typeId = listing.typeId,
            recordSource = listing.recordSource,
            zipCode = listing.zipCode,
            msaCode = listing.msaCode,
            unitSquareFootage = listing.unitSquareFootage,
            bedroomsQuantity = listing.bedroomsQuantity,
            bathroomsQuantity = listing.bathroomsQuantity,
            floorPlan = listing.floorPlan,
        ).also(effectiveRentRepository::save)

    companion object {
        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))
    }

    @Test
    fun `Should retrieve listings for a given property using default period`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USTX-027626",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("999.00"),
                            listingFrom = LocalDate.parse("2023-10-05"),
                            listingTo = LocalDate.parse("2023-10-07"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-14"),
                            listingTo = LocalDate.parse("2023-10-23"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "225",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1093.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result = Unirest.get(givenUrl).asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given property using default period with bedrooms filter`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USTX-027626",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("999.00"),
                            listingFrom = LocalDate.parse("2023-10-05"),
                            listingTo = LocalDate.parse("2023-10-07"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-14"),
                            listingTo = LocalDate.parse("2023-10-23"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(mapOf(BEDROOMS_PARAM_NAME to "3"))
                .asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given property using default period with bathrooms filter`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USTX-027626",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "225",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1093.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(mapOf(BATHROOMS_PARAM_NAME to "1.5"))
                .asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given property using default period and unitId filter`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USTX-027626",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("999.00"),
                            listingFrom = LocalDate.parse("2023-10-05"),
                            listingTo = LocalDate.parse("2023-10-07"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-14"),
                            listingTo = LocalDate.parse("2023-10-23"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(mapOf(UNIT_ID_PARAM_NAME to "236"))
                .asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given property in a given period`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USTX-027626",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "141",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-04"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A3",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-08-07"),
                            listingTo = LocalDate.parse("2023-08-10"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to "2023-08-01",
                        DATE_TO_PARAM_NAME to "2023-08-15",
                    ),
                ).asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve floor plan listings for a given property in a given period`() {
        // Given
        val propertyId = "USGA-004090"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USGA-004090",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.FLOOR_PLAN,
                            unitId = null,
                            floorPlan = "C1",
                            askingRent = BigDecimal("1310.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-10"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("800.00"),
                            squareFootage = BigDecimal("550"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.FLOOR_PLAN,
                            unitId = null,
                            floorPlan = "C2",
                            askingRent = BigDecimal("1300.00"),
                            listingFrom = LocalDate.parse("2023-08-10"),
                            listingTo = LocalDate.parse("2023-08-12"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1"),
                            deposit = BigDecimal("500.00"),
                            squareFootage = BigDecimal("795"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to "2023-08-01",
                        DATE_TO_PARAM_NAME to "2023-08-15",
                        LISTING_TYPE_PARAM_NAME to RentListingType.FLOOR_PLAN.name,
                    ),
                ).asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Given a property with listings of both types should retrieve all when type filter is not provided`() {
        // Given
        val propertyId = "USGA-004090"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            PropertiesListingsResponse(
                propertyId = "USGA-004090",
                listings =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.FLOOR_PLAN,
                            unitId = null,
                            floorPlan = "C1",
                            askingRent = BigDecimal("1310.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-10"),
                            availableOn = null,
                            bedrooms = 3,
                            bathrooms = BigDecimal("2"),
                            deposit = BigDecimal("800.00"),
                            squareFootage = BigDecimal("550"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.FLOOR_PLAN,
                            unitId = null,
                            floorPlan = "C2",
                            askingRent = BigDecimal("1300.00"),
                            listingFrom = LocalDate.parse("2023-08-10"),
                            listingTo = LocalDate.parse("2023-08-12"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1"),
                            deposit = BigDecimal("500.00"),
                            squareFootage = BigDecimal("795"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "C 216",
                            floorPlan = "C2",
                            askingRent = BigDecimal("1100.00"),
                            listingFrom = LocalDate.parse("2023-08-10"),
                            listingTo = LocalDate.parse("2023-08-12"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1"),
                            deposit = BigDecimal("350.00"),
                            squareFootage = BigDecimal("795"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to "2023-08-01",
                        DATE_TO_PARAM_NAME to "2023-08-15",
                    ),
                ).asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.listings.size, result.body.listings.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.listings.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should not retrieve unit rent data since property doesn't exists`() {
        // Given
        val propertyId = "USTX-999999"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/listings"

        val elements = DataUtils.listingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }
        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(PropertiesListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertFalse(result.isSuccess)
        assertEquals(result.status, HttpStatus.NOT_FOUND)
    }
}

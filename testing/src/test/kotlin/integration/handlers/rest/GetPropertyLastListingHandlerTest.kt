package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import integration.handlers.utils.DataUtils
import io.kotest.matchers.collections.shouldContainAll
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.koin.test.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class GetPropertyLastListingHandlerTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    fun createEffective(listing: RentListing) =
        EffectiveRent(
            id = UUID.randomUUID().toString(),
            rentListingId = listing.id,
            concessionIds = listOf(),
            dateFrom = listing.dateFrom,
            dateTo = listing.dateTo,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            concessions = "",
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
            propertyId = listing.propertyId,
            type = listing.type,
            typeId = listing.typeId,
            recordSource = listing.recordSource,
            zipCode = listing.zipCode,
            msaCode = listing.msaCode,
            unitSquareFootage = listing.unitSquareFootage,
            bedroomsQuantity = listing.bedroomsQuantity,
            bathroomsQuantity = listing.bathroomsQuantity,
            floorPlan = listing.floorPlan,
        ).also(effectiveRentRepository::save)

    @Test
    fun `Should retrieve unit rent data for a given property`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/units"

        val elements = DataUtils.unitRentData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }
        // When
        val result = Unirest.get(givenUrl).asObject(RentListingsResponse::class.java)

        val expectedResponse =
            RentListingsResponse(
                propertyId = "USTX-027626",
                units =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "141",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-04"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-09-29"),
                            listingTo = LocalDate.parse("2023-09-29"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.units.size, result.body.units.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve unit rent data for a given property filtered by dates`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/units"

        val elements = DataUtils.unitRentData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(DATE_FROM_PARAM_NAME, "2023-07-01")
                .queryString(DATE_TO_PARAM_NAME, "2023-09-30")
                .asObject(RentListingsResponse::class.java)

        val expectedResponse =
            RentListingsResponse(
                propertyId = "USTX-027626",
                units =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "141",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-04"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-09-29"),
                            listingTo = LocalDate.parse("2023-09-29"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-09-29"),
                            listingTo = LocalDate.parse("2023-09-29"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.units.size, result.body.units.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve unit rent data for a given property filtered by date_from`() {
        // Given
        val propertyId = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/units"

        val elements = DataUtils.unitRentData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }
        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(DATE_FROM_PARAM_NAME, "2023-09-01")
                .asObject(RentListingsResponse::class.java)

        val expectedResponse =
            RentListingsResponse(
                propertyId = "USTX-027626",
                units =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-09-29"),
                            listingTo = LocalDate.parse("2023-09-29"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                    ),
            )

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.units.size, result.body.units.size)

        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.units.map { listing ->
                listing.copy(effectiveRents = emptyList())
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should not retrieve unit rent data since property doesn't exists`() {
        // Given
        val propertyId = "USTX-999999"
        val givenUrl = "${localUrl()}/multifamily/$propertyId/units"

        val elements = DataUtils.unitRentData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }
        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(RentListingsResponse::class.java)

        // Then
        assertNotNull(result)
        assertFalse(result.isSuccess)
        assertEquals(result.status, HttpStatus.NOT_FOUND)
    }
}

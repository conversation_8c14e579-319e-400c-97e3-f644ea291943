package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.EffectiveRentResponse
import com.keyway.adapters.dtos.listings.PropertiesListingsResponse
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.application.utils.router.ParamUtils.BATHROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.BEDROOMS_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.PROP_IDS_PARAM_NAME
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import integration.handlers.utils.DataUtils
import io.kotest.matchers.collections.shouldContainAll
import kong.unirest.GenericType
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class GetPropertiesListingsHandlerTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    companion object {
        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))
    }

    fun createEffective(listing: RentListing): EffectiveRent =
        EffectiveRent(
            id = UUID.randomUUID().toString(),
            rentListingId = listing.id,
            concessionIds = listOf(),
            dateFrom = listing.dateFrom,
            dateTo = listing.dateTo,
            rent = listing.rent,
            rentDeposit = listing.rentDeposit,
            concessions = "",
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
            propertyId = listing.propertyId,
            type = listing.type,
            typeId = listing.typeId,
            recordSource = listing.recordSource,
            zipCode = listing.zipCode,
            msaCode = listing.msaCode,
            unitSquareFootage = listing.unitSquareFootage,
            bedroomsQuantity = listing.bedroomsQuantity,
            bathroomsQuantity = listing.bathroomsQuantity,
            floorPlan = listing.floorPlan,
        ).also(effectiveRentRepository::save)

    @Test
    fun `Should retrieve listings for a single property default period`() {
        // Given
        val propertiesList = "USTX-027626"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val listings = DataUtils.multipleListingsData
        listings.forEach(listingsRepository::save)
        val effectiveRent = createEffective(listings.first())

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027626",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(effectiveRent.toResponse()),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given list of properties using default period`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027626",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = "USTX-027329",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "289",
                                floorPlan = "A3",
                                askingRent = BigDecimal("999.00"),
                                listingFrom = LocalDate.parse("2023-10-05"),
                                listingTo = LocalDate.parse("2023-10-07"),
                                availableOn = null,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = "USTX-027777",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "225",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1093.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given list of properties using custom period`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027626",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = "USTX-027329",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "289",
                                floorPlan = "A3",
                                askingRent = BigDecimal("999.00"),
                                listingFrom = LocalDate.parse("2023-10-05"),
                                listingTo = LocalDate.parse("2023-10-07"),
                                availableOn = null,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = "USTX-027777",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "225",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1093.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given list of properties using default period with bedrooms filter`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
            createEffective(it)
        }

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = "USTX-027329",
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "289",
                                floorPlan = "A3",
                                askingRent = BigDecimal("999.00"),
                                listingFrom = LocalDate.parse("2023-10-05"),
                                listingTo = LocalDate.parse("2023-10-07"),
                                availableOn = null,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        BEDROOMS_PARAM_NAME to 3,
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Should retrieve listings for a given list of properties using default period with bathrooms filter`() {
        // Given
        val propertiesList = "USTX-027626,USTX-027329,USTX-027777,USTX-022222"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val elements = DataUtils.multipleListingsData
        elements.forEach(listingsRepository::save)
        val firstPropertyId = "USTX-027626"
        val secondPropertyId = "USTX-027777"
        val firstListingEffectiveRent = createEffective(elements.first { it.propertyId == firstPropertyId })
        val secondListingEffectiveRent = createEffective(elements.first { it.propertyId == secondPropertyId })

        val expectedResponse =
            listOf(
                PropertiesListingsResponse(
                    propertyId = firstPropertyId,
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(firstListingEffectiveRent.toResponse()),
                                active = false,
                            ),
                        ),
                ),
                PropertiesListingsResponse(
                    propertyId = secondPropertyId,
                    listings =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "225",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1093.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(secondListingEffectiveRent.toResponse()),
                                active = false,
                            ),
                        ),
                ),
            )

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                        BATHROOMS_PARAM_NAME to "1.5",
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(result.status, HttpStatus.OK)
        assertEquals(expectedResponse.size, result.body.size)
        // Compare responses ignoring effectiveRents
        val actualResponseWithoutEffectiveRents =
            result.body.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        val expectedResponseWithoutEffectiveRents =
            expectedResponse.map { propertyResponse ->
                propertyResponse.copy(
                    listings =
                        propertyResponse.listings.map { listing ->
                            listing.copy(effectiveRents = emptyList())
                        },
                )
            }
        actualResponseWithoutEffectiveRents.shouldContainAll(expectedResponseWithoutEffectiveRents)
    }

    @Test
    fun `Shouldn't retrieve listings because of property does not exist`() {
        // Given
        val propertiesList = "USTX-111111,USTX-011112"
        val givenUrl = "${localUrl()}/multifamily/listings"

        val elements = DataUtils.multipleListingsData
        elements.forEach {
            listingsRepository.save(it)
        }

        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        PROP_IDS_PARAM_NAME to propertiesList,
                    ),
                ).asObject(object : GenericType<List<PropertiesListingsResponse>>() {})

        // Then
        assertNotNull(result)
        assertFalse(result.isSuccess)
        assertEquals(result.status, HttpStatus.NOT_FOUND)
    }

    private fun EffectiveRent.toResponse() =
        EffectiveRentResponse(
            dateFrom = this.dateFrom,
            dateTo = this.dateTo,
            rent = this.rent.value,
            rentDeposit = this.rentDeposit?.value,
        )
}

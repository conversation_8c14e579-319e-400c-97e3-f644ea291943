package integration.handlers.rest

import application.utils.base.BaseApplicationTest
import com.keyway.application.utils.router.ParamUtils.DATE_FROM_PARAM_NAME
import com.keyway.application.utils.router.ParamUtils.DATE_TO_PARAM_NAME
import com.keyway.core.entities.concessions.ConcessionTypesDistribution
import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import io.kotest.matchers.shouldBe
import kong.unirest.HttpStatus
import kong.unirest.Unirest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class GetConcessionTypesDistributionTest : BaseApplicationTest() {
    private val propertyConcessionV2Repository: PropertyConcessionV2Repository by inject()

    @BeforeEach
    fun setUp() {
        DateHelper.setClockUTC(Instant.parse("2024-06-15T00:00:00Z"))
    }

    private val testMsaCode = "12345"
    private val testZipCode = "77008"
    private val testDate = LocalDate.parse("2024-06-01")

    private val testConcessionsData =
        listOf(
            // Free rent concessions
            PropertyConcessionV2(
                id = "concession-1",
                propertyId = "USTX-001",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "2 months free rent",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("2.00"),
                            conditionBedrooms = listOf(1, 2),
                            oneTimeDollarsOffAmount = null,
                            recurringDollarsOffAmount = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            ),
            // One-time discount concession
            PropertyConcessionV2(
                id = "concession-2",
                propertyId = "USTX-002",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "$500 off first month",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            conditionBedrooms = listOf(1),
                            oneTimeDollarsOffAmount = BigDecimal("500.00"),
                            recurringDollarsOffAmount = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            ),
            // Recurring discount concession
            PropertyConcessionV2(
                id = "concession-3",
                propertyId = "USTX-003",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "$100 off monthly rent",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            conditionBedrooms = listOf(2),
                            oneTimeDollarsOffAmount = null,
                            recurringDollarsOffAmount = BigDecimal("100.00"),
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            ),
            // Waived fees concession
            PropertyConcessionV2(
                id = "concession-4",
                propertyId = "USTX-004",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "Waived application fee",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            conditionBedrooms = listOf(1, 2, 3),
                            oneTimeDollarsOffAmount = null,
                            recurringDollarsOffAmount = null,
                            waivedApplicationFee = true,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            ),
            // Cheaper fees concession
            PropertyConcessionV2(
                id = "concession-5",
                propertyId = "USTX-005",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "Reduced security deposit",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = null,
                            conditionBedrooms = listOf(1),
                            oneTimeDollarsOffAmount = null,
                            recurringDollarsOffAmount = null,
                            waivedApplicationFee = null,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = true,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            ),
        )

    @Test
    fun `should return concession types distribution for MSA code`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/concession-types-distribution"

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 1,
                oneTimeDiscount = 1,
                recurringDiscount = 1,
                cheaperFees = 1,
                waivedFees = 1,
                total = 5,
                totalProperties = 5,
            )

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should return concession types distribution for ZIP code`() {
        // Given
        val givenUrl = "${localUrl()}/zip-codes/$testZipCode/concession-types-distribution"

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 1,
                oneTimeDiscount = 1,
                recurringDiscount = 1,
                cheaperFees = 1,
                waivedFees = 1,
                total = 5,
                totalProperties = 5,
            )

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should return empty distribution for non-existent MSA code`() {
        // Given
        val nonExistentMsaCode = "99999"
        val givenUrl = "${localUrl()}/msa-codes/$nonExistentMsaCode/concession-types-distribution"

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 0,
                oneTimeDiscount = 0,
                recurringDiscount = 0,
                cheaperFees = 0,
                waivedFees = 0,
                total = 0,
                totalProperties = 0,
            )

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should return empty distribution for non-existent ZIP code`() {
        // Given
        val nonExistentZipCode = "99999"
        val givenUrl = "${localUrl()}/zip-codes/$nonExistentZipCode/concession-types-distribution"

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 0,
                oneTimeDiscount = 0,
                recurringDiscount = 0,
                cheaperFees = 0,
                waivedFees = 0,
                total = 0,
                totalProperties = 0,
            )

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should work without date parameters using defaults`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/concession-types-distribution"

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        // When
        val result =
            Unirest
                .get(givenUrl)
                .asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        // Should return some results since default date range should include our test data
        assertTrue(result.body.total >= 0)
    }

    @Test
    fun `should filter by date range correctly`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/concession-types-distribution"

        // Add concession outside the query date range
        val outsideDateConcession =
            testConcessionsData[0].copy(
                id = "outside-date-concession",
                dateFrom = testDate.minusDays(60),
                dateTo = testDate.minusDays(50),
            )

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }
        propertyConcessionV2Repository.saveOrUpdate(outsideDateConcession)

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 1,
                oneTimeDiscount = 1,
                recurringDiscount = 1,
                cheaperFees = 1,
                waivedFees = 1,
                total = 5,
                totalProperties = 5,
            )

        // When - query only for the original date range
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then - should not include the outside date concession
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should count multiple concessions of same type correctly`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/concession-types-distribution"

        // Add additional free rent concessions
        val additionalFreeRentConcessions =
            listOf(
                testConcessionsData[0].copy(
                    id = "free-rent-2",
                    propertyId = "USTX-006",
                ),
                testConcessionsData[0].copy(
                    id = "free-rent-3",
                    propertyId = "USTX-007",
                ),
            )

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }
        additionalFreeRentConcessions.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }

        val expectedResponse =
            ConcessionTypesDistribution(
                freeRent = 3, // Original 1 + 2 additional
                oneTimeDiscount = 1,
                recurringDiscount = 1,
                cheaperFees = 1,
                waivedFees = 1,
                total = 7, // 5 original + 2 additional
                totalProperties = 7,
            )

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        result.body shouldBe expectedResponse
    }

    @Test
    fun `should handle concessions with multiple benefit types`() {
        // Given
        val givenUrl = "${localUrl()}/msa-codes/$testMsaCode/concession-types-distribution"

        // Create a concession with multiple benefits (should be counted in multiple categories)
        val multiBenefitConcession =
            PropertyConcessionV2(
                id = "multi-benefit-concession",
                propertyId = "USTX-008",
                dateFrom = testDate,
                dateTo = testDate.plusDays(30),
                concessionText = "1 month free + waived application fee",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("1.00"),
                            conditionBedrooms = listOf(1),
                            oneTimeDollarsOffAmount = null,
                            recurringDollarsOffAmount = null,
                            waivedApplicationFee = true,
                            waivedSecurityDeposit = null,
                            waivedAdministrativeFee = null,
                            waivedMoveInFee = null,
                            cheaperSecurityDeposit = null,
                            cheaperAdministrativeFee = null,
                            cheaperApplicationFee = null,
                            cheaperMoveInFee = null,
                            cheaperRent = null,
                            benefitGroupId = null,
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00Z"),
                zipCode = testZipCode,
                msaCode = testMsaCode,
            )

        testConcessionsData.forEach {
            propertyConcessionV2Repository.saveOrUpdate(it)
        }
        propertyConcessionV2Repository.saveOrUpdate(multiBenefitConcession)

        // When
        val result =
            Unirest
                .get(givenUrl)
                .queryString(
                    mapOf(
                        DATE_FROM_PARAM_NAME to testDate,
                        DATE_TO_PARAM_NAME to testDate.plusDays(30),
                    ),
                ).asObject(ConcessionTypesDistribution::class.java)

        // Then
        assertNotNull(result)
        assertTrue(result.isSuccess)
        assertEquals(HttpStatus.OK, result.status)
        // The multi-benefit concession should contribute to both freeRent and waivedFees counts
        assertTrue(result.body.freeRent >= 2) // Original 1 + multi-benefit 1
        assertTrue(result.body.waivedFees >= 2) // Original 1 + multi-benefit 1
        assertTrue(result.body.total >= 6) // Original 5 + multi-benefit 1
    }
}

package integration.handlers.consumers

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.GroupedRentListingMessage
import com.keyway.adapters.dtos.listings.RentUnitListingMessage
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.findQueueConfiguration
import com.keyway.application.koin.ModuleConstants
import com.keyway.core.dto.query.listings.PropertiesListingsQuery
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import com.keyway.kommons.aws.config.SqsConfig
import com.keyway.kommons.sqs.IMessageHandler
import com.keyway.kommons.sqs.buildSqsClient
import io.kotest.matchers.collections.shouldContainAll
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.core.qualifier.named
import org.koin.test.get
import org.koin.test.inject
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.sqs.model.SendMessageRequest
import utils.DateHelper
import utils.MockedEntityFactory
import utils.SqsHelpers.buildLocalstackQueueUrl
import utils.SqsHelpers.cleanQueue
import utils.SqsHelpers.receiveMessages
import utils.SqsHelpers.sendOversizeMessage
import utils.SqsHelpers.start
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

class SaveGroupedListingIntegrationTest : BaseApplicationTest() {
    companion object {
        @JvmStatic
        @BeforeAll
        fun startConsumers() {
            consumers.start()
        }

        @JvmStatic
        @BeforeAll
        fun initClock() = DateHelper.setClockUTC(Instant.parse("2024-07-27T00:00:00Z"))
    }

    private val config: Configuration by inject()
    private val client = buildSqsClient(config.awsConfig)
    private val s3Client: S3Client by inject()
    private val queueName = get<SqsConfig>().findQueueConfiguration("grouped_unit_rent_data").queueName
    private val queueUrl = buildLocalstackQueueUrl(queueName)

    private val listingsRepository: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()

    private val saveGroupedListingHandler: IMessageHandler by inject(
        named(ModuleConstants.GROUPED_UNIT_RENT_DATA_HANDLER),
    )

    @Test
    fun `should consume the event and create the necessary listings`() {
        val entityId = UUID.randomUUID().toString()
        val effectiveRentId = UUID.randomUUID().toString()
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-20"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-21"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-22"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-25"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                    ),
            )

        idGenerator.setId(entityId)
        idGenerator.setId(effectiveRentId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val queryDate = LocalDate.parse("2024-07-27")
        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findMostRecentListingByPropertyAndUnit(propertyId, rentListingType, unitId, queryDate)
                }
            }

        val expectedEntity =
            RentListing(
                id = buildId("014695", "305", entityId),
                propertyId = "USFL-014695",
                type = RentListingType.UNIT,
                typeId = "9-305",
                rent = Money.of("1525.00"),
                dateFrom = LocalDate.parse("2024-07-20"),
                dateTo = LocalDate.parse("2024-07-26"),
                recordSource = "apartments",
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now<OffsetDateTime>(),
                updateAt = DateUtils.now<OffsetDateTime>(),
            )

        assertNotNull(result)
        assertEquals(expectedEntity, result)

        val effectiveData = effectiveRentRepository.getByListingId(result?.id!!)
        assertEquals(1, effectiveData.size)
        assertEquals(effectiveData.first().rent, Money.of("1525.00"))
        assertEquals(effectiveData.first().dateTo, LocalDate.parse("2024-07-26"))
    }

    @Test
    fun `should consume the event and create two listings`() {
        val entityId = UUID.randomUUID().toString()
        val secondEntityId = UUID.randomUUID().toString()

        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-20"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-21"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-22"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-25"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                    ),
            )

        idGenerator.setId(entityId)
        idGenerator.setId(UUID.randomUUID().toString())
        idGenerator.setId(secondEntityId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val queryDate = LocalDate.parse("2024-07-27")
        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findMostRecentListingByPropertyAndUnit(propertyId, rentListingType, unitId, queryDate)
                }
            }

        val expectedEntity =
            RentListing(
                id = buildId("014695", "305", secondEntityId),
                propertyId = "USFL-014695",
                type = RentListingType.UNIT,
                typeId = "9-305",
                rent = Money.of("1525.00"),
                dateFrom = LocalDate.parse("2024-07-23"),
                dateTo = LocalDate.parse("2024-07-26"),
                recordSource = "apartments",
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            )

        assertNotNull(result)
        assertEquals(expectedEntity, result)

        val effectiveData = effectiveRentRepository.getByListingId(result?.id!!)
        assertEquals(1, effectiveData.size)
        assertEquals(effectiveData.first().rent, Money.of("1525.00"))
        assertEquals(effectiveData.first().dateTo, LocalDate.parse("2024-07-26"))
    }

    @Test
    fun `should consume the oversize events`() {
        val entityId = UUID.randomUUID().toString()

        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT
        val days = 10000
        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                records =
                    (1..days).map {
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = DateUtils.now<LocalDate>().minusDays(it.toLong()),
                        )
                    },
            )

        idGenerator.setId(entityId)
        sendOversizeMessage(
            client = client,
            queueUrl = queueUrl,
            message = message,
            s3Client = s3Client,
        )

        val queryDate: LocalDate = DateUtils.now()
        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findMostRecentListingByPropertyAndUnit(propertyId, rentListingType, unitId, queryDate)
                }
            }

        assertNotNull(result)

        val effectiveData = effectiveRentRepository.getByListingId(result?.id!!)
        assertEquals(1, effectiveData.size)
        assertEquals(effectiveData.first().rent, Money.of("1325.00"))
    }

    @Test
    fun `should handler the oversize events`() {
        val entityId = UUID.randomUUID().toString()

        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT
        val days = 1000
        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                records =
                    (0..days).map {
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = DateUtils.now<LocalDate>().minusDays(it.toLong()),
                        )
                    },
            )

        idGenerator.setId(entityId)
        val result =
            saveGroupedListingHandler.processMessage(
                software.amazon.awssdk.services.sqs.model.Message
                    .builder()
                    .body(SqsMapper.encode(message))
                    .build(),
            )
        assertTrue(result)

        val queryDate: LocalDate = DateUtils.now()
        val listing = listingsRepository.findMostRecentListingByPropertyAndUnit(propertyId, rentListingType, unitId, queryDate)

        val expectedEntity =
            RentListing(
                id = buildId("014695", "305", entityId),
                propertyId = "USFL-014695",
                type = RentListingType.UNIT,
                typeId = unitId,
                rent = Money.of("1325.00"),
                dateFrom = DateUtils.now<LocalDate>().minusDays(days.toLong()),
                dateTo = DateUtils.now(),
                recordSource = "apartments",
                zipCode = "33009",
                msaCode = "35620",
                unitSquareFootage = BigDecimal("1119"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            )

        assertNotNull(result)
        assertEquals(expectedEntity, listing)
        val effectiveData = effectiveRentRepository.getByListingId(listing?.id!!)
        assertEquals(1, effectiveData.size)
        assertEquals(effectiveData.first().rent, Money.of("1325.00"))
        assertEquals(effectiveData.first().dateTo, DateUtils.now<LocalDate>())
    }

    private fun buildId(
        propertyId: String,
        typeId: String,
        uuid: String,
    ) = "$propertyId-$typeId-$uuid"

    @Test
    fun `should handle the event aggregating 2 listings and delete 1`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val firstListingId = buildId("014695", "305", "firstListingId")
        val secondListingId = buildId("014695", "305", "secondListingId")
        val thirdListingId = buildId("014695", "305", "thirdListingId")

        val listings =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = secondListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        listingsRepository.save(listings)

        val effectiveRent =
            listOf(
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == firstListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == secondListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == thirdListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    rent = Money.of("1725.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )
        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-12"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-13"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-14"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-15"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-16"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-17"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-18"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-19"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-12"),
                            dateTo = LocalDate.parse("2024-07-25"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        result.shouldContainAll(expectedResult)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, it.rent)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should handle the event aggregating listings without delete`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val firstListingId = "3a6adb3d-ef8d-4228-9255-49ac24272db4"
        val secondListingId = "f1bbfc0c-b36c-4167-9466-398a89c25303"
        val thirdListingId = "38136e11-5e64-41c6-b80a-895c5422ec11"

        val listings =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = secondListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        listingsRepository.save(listings)

        val effectiveRent =
            listOf(
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == firstListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == secondListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == thirdListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    rent = Money.of("1725.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-12"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-13"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-14"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2024-09-15"),
                            recordDate = LocalDate.parse("2024-07-15"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-12"),
                            dateTo = LocalDate.parse("2024-07-25"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        expectedResult.shouldContainAll(result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, it.rent)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should handle the event aggregating listings updating listings`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val firstListingId = "3a6adb3d-ef8d-4228-9255-49ac24272db4"
        val secondListingId = UUID.randomUUID().toString()

        val listings =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-14"),
                    dateTo = LocalDate.parse("2024-07-14"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        listingsRepository.save(listings)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-12"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-13"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-14"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1525.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1525.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-15"),
                        ),
                    ),
            )

        idGenerator.setId(secondListingId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-12"),
                            dateTo = LocalDate.parse("2024-07-25"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = buildId("014695", "305", secondListingId),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-15"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, it.rent)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should handle the event aggregating listings in the past without delete`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val firstListingId = "3a6adb3d-ef8d-4228-9255-49ac24272db4"
        val secondListingId = "f1bbfc0c-b36c-4167-9466-398a89c25303"
        val thirdListingId = "38136e11-5e64-41c6-b80a-895c5422ec11"

        val firstNewListingId = UUID.randomUUID().toString()
        val secondNewListingId = UUID.randomUUID().toString()

        val listings =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = secondListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        listingsRepository.save(listings)

        val effectiveRent =
            listOf(
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == firstListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == secondListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    rent = Money.of("1525.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = UUID.randomUUID().toString(),
                    listing = listings.first { f -> f.id == thirdListingId },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    rent = Money.of("1725.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1125.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1125.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-06-12"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1125.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1125.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-06-13"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-06-14"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "apartments",
                            rent = BigDecimal("1325.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1325.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-06-15"),
                        ),
                    ),
            )
        idGenerator.setId(firstNewListingId)
        idGenerator.setId(UUID.randomUUID().toString())
        idGenerator.setId(secondNewListingId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-06-12"),
                            dateTo = LocalDate.parse("2024-07-25"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = buildId("014695", "305", firstNewListingId),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1125.00"),
                    dateFrom = LocalDate.parse("2024-06-12"),
                    dateTo = LocalDate.parse("2024-06-13"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = buildId("014695", "305", secondNewListingId),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1325.00"),
                    dateFrom = LocalDate.parse("2024-06-14"),
                    dateTo = LocalDate.parse("2024-06-15"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-12"),
                    dateTo = LocalDate.parse("2024-07-12"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = secondListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1525.00"),
                    dateFrom = LocalDate.parse("2024-07-20"),
                    dateTo = LocalDate.parse("2024-07-20"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = thirdListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1725.00"),
                    dateFrom = LocalDate.parse("2024-07-25"),
                    dateTo = LocalDate.parse("2024-07-25"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2024-09-15"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        result.shouldContainAll(expectedResult)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, it.rent)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should consume the event and create the necessary listings updating ones`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val firstListingId = "a7daa1bf-5153-4363-a4e7-41cab6e4f836"
        val effectiveRentId = UUID.randomUUID().toString()

        val listings =
            listOf(
                MockedEntityFactory.buildRentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    rent = Money.of("1540.00"),
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    dateFrom = LocalDate.parse("2024-07-24"),
                    dateTo = LocalDate.parse("2024-07-29"),
                    availableIn = null,
                    rentDeposit = Money.of("400.00"),
                ),
            )

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    id = effectiveRentId,
                    listing = listings.first(),
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-24"),
                    dateTo = LocalDate.parse("2024-07-29"),
                    rentDeposit = null,
                ),
            )
        listingsRepository.save(listings)
        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1540.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-30"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1540.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-31"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1540.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-01"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1540.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-02"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1540.00"),
                            effectiveRentDeposit = null,
                            availableIn = LocalDate.parse("2025-08-02"),
                            recordDate = LocalDate.parse("2024-08-03"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-22"),
                            dateTo = LocalDate.parse("2024-09-01"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-24"),
                    dateTo = LocalDate.parse("2024-08-03"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = LocalDate.parse("2025-08-02"),
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveRentId, effectiveData.first().id)
            assertEquals(effectiveData.first().rent, it.rent)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should consume the event and repair the existing listing`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val properEffectiveRent = Money.of(BigDecimal("1340.00"))
        val rentListingType = RentListingType.UNIT

        val firstListingId = "a7daa1bf-5153-4363-a4e7-41cab6e4f836"
        val effectiveRentId = UUID.randomUUID().toString()
        val newListingId = UUID.randomUUID().toString()
        val newEffectiveRentId = UUID.randomUUID().toString()

        val listings =
            listOf(
                MockedEntityFactory.buildRentListing(
                    id = firstListingId,
                    propertyId = propertyId,
                    typeId = unitId,
                    rent = Money.of("1540.00"),
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    dateFrom = LocalDate.parse("2024-07-24"),
                    dateTo = LocalDate.parse("2024-07-24"),
                    availableIn = null,
                    rentDeposit = Money.of("400.00"),
                ),
            )

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    id = effectiveRentId,
                    listing = listings.first(),
                    rent = Money.of("4000.00"),
                    dateFrom = LocalDate.parse("2024-07-24"),
                    dateTo = LocalDate.parse("2024-07-24"),
                    rentDeposit = null,
                ),
            )
        listingsRepository.save(listings)
        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-25"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-27"),
                        ),
                    ),
            )

        idGenerator.setId(newListingId)
        idGenerator.setId(newEffectiveRentId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-22"),
                            dateTo = LocalDate.parse("2024-09-01"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = buildId("014695", "305", newListingId),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-23"),
                    dateTo = LocalDate.parse("2024-07-27"),
                    recordSource = "rent",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(buildId("014695", "305", newEffectiveRentId), effectiveData.first().id)
            assertEquals(effectiveData.first().rent, properEffectiveRent)
            assertEquals(effectiveData.first().dateFrom, it.dateFrom)
            assertEquals(effectiveData.first().dateTo, it.dateTo)

            val oldEffectiveData = effectiveRentRepository.getByListingId(firstListingId)
            assertEquals(0, oldEffectiveData.size)
        }
    }

    @Test
    fun `should consume the event and create the listing with less than 7 days gap`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val properEffectiveRent = Money.of(BigDecimal("1340.00"))
        val rentListingType = RentListingType.UNIT

        val newListingId = UUID.randomUUID().toString()
        val newEffectiveRentId = UUID.randomUUID().toString()

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-27"),
                        ),
                    ),
            )

        idGenerator.setId(newListingId)
        idGenerator.setId(newEffectiveRentId)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-01"),
                            dateTo = LocalDate.parse("2024-07-31"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = buildId("014695", "305", newListingId),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-23"),
                    dateTo = LocalDate.parse("2024-07-27"),
                    recordSource = "rent",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(buildId("014695", "305", newEffectiveRentId), effectiveData.first().id)
            assertEquals(effectiveData.first().rent, properEffectiveRent)
            assertEquals(effectiveData.first().dateFrom, it.dateFrom)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should consume the event and create two listings because of more than 7 days gap`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val properEffectiveRent = Money.of(BigDecimal("1340.00"))
        val rentListingType = RentListingType.UNIT

        val listingId1 = "listingId1"
        val listingId2 = "listingId2"
        val effectiveRentId1 = "effectiveRentId1"
        val effectiveRentId2 = "effectiveRentId2"

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-10"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-11"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-27"),
                        ),
                    ),
            )

        idGenerator.setId(listingId1)
        idGenerator.setId(effectiveRentId1)
        idGenerator.setId(listingId2)
        idGenerator.setId(effectiveRentId2)
        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-01"),
                            dateTo = LocalDate.parse("2024-07-31"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = buildId("014695", "305", listingId1),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-10"),
                    dateTo = LocalDate.parse("2024-07-11"),
                    recordSource = "rent",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = buildId("014695", "305", listingId2),
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-26"),
                    dateTo = LocalDate.parse("2024-07-27"),
                    recordSource = "rent",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, properEffectiveRent)
            assertEquals(effectiveData.first().dateFrom, it.dateFrom)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should consume the event and repair the wrong listings because of the 7 days gap`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val properEffectiveRent = Money.of(BigDecimal("1340.00"))
        val rentListingType = RentListingType.UNIT

        val listingId1 = UUID.randomUUID().toString()
        val listingId2 = UUID.randomUUID().toString()
        val effectiveRentId1 = UUID.randomUUID().toString()
        val effectiveRentId2 = UUID.randomUUID().toString()

        val listings =
            listOf(
                RentListing(
                    id = listingId1,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-02"),
                    dateTo = LocalDate.parse("2024-07-04"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = listingId2,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-06"),
                    dateTo = LocalDate.parse("2024-07-08"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        val effectiveRent =
            listOf(
                EffectiveRent.build(
                    id = effectiveRentId1,
                    listing = listings.first { f -> f.id == listingId1 },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-02"),
                    dateTo = LocalDate.parse("2024-07-04"),
                    rent = Money.of("1340.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                EffectiveRent.build(
                    id = effectiveRentId2,
                    listing = listings.first { f -> f.id == listingId2 },
                    concessionIds = emptyList(),
                    dateFrom = LocalDate.parse("2024-07-06"),
                    dateTo = LocalDate.parse("2024-07-08"),
                    rent = Money.of("1340.00"),
                    rentDeposit = null,
                    concessions = "",
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        setupExistingListings(listings, effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-02"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-03"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-04"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-06"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-07"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = BigDecimal("400"),
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-08"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-07-01"),
                            dateTo = LocalDate.parse("2024-07-31"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = listingId1,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("1540.00"),
                    dateFrom = LocalDate.parse("2024-07-02"),
                    dateTo = LocalDate.parse("2024-07-08"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = Money.of("400"),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(1, effectiveData.size)
            assertEquals(effectiveData.first().rent, properEffectiveRent)
            assertEquals(effectiveData.first().dateFrom, it.dateFrom)
            assertEquals(effectiveData.first().dateTo, it.dateTo)
        }
    }

    @Test
    fun `should repair the incorrect listings`() {
        val propertyId = "USFL-014695"
        val unitId = "9-305"
        val rentListingType = RentListingType.UNIT

        val listingId1 = UUID.randomUUID().toString()
        val effectiveRentId1 = UUID.randomUUID().toString()
        val listing =
            MockedEntityFactory.buildRentListing(
                propertyId = propertyId,
                typeId = unitId,
                id = listingId1,
                rent = Money.of("2400.00"),
                dateFrom = LocalDate.parse("2024-07-18"),
                dateTo = LocalDate.parse("2024-08-16"),
                availableIn = null,
                rentDeposit = null,
            )

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    id = effectiveRentId1,
                    listing = listing,
                    dateFrom = LocalDate.parse("2024-07-18"),
                    dateTo = LocalDate.parse("2024-08-09"),
                    rent = Money.of("2200.00"),
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    dateFrom = LocalDate.parse("2024-08-04"),
                    dateTo = LocalDate.parse("2024-08-05"),
                    rent = Money.of("2400.00"),
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    dateFrom = LocalDate.parse("2024-08-10"),
                    dateTo = LocalDate.parse("2024-08-10"),
                    rent = Money.of("2400.00"),
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    dateFrom = LocalDate.parse("2024-08-11"),
                    dateTo = LocalDate.parse("2024-08-12"),
                    rent = Money.of("2200.00"),
                ),
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    dateFrom = LocalDate.parse("2024-08-13"),
                    dateTo = LocalDate.parse("2024-08-16"),
                    rent = Money.of("2400.00"),
                ),
            )

        setupExistingListings(listOf(listing), effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = propertyId,
                type = rentListingType,
                unitId = unitId,
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2200.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-22"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-25"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-26"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-27"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2400.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-28"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("2400.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("2200.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-08-29"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(propertyId),
                            type = rentListingType,
                            typeId = unitId,
                            dateFrom = LocalDate.parse("2024-08-15"),
                            dateTo = LocalDate.parse("2024-09-05"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        val expectedResult =
            listOf(
                RentListing(
                    id = listingId1,
                    propertyId = propertyId,
                    typeId = unitId,
                    type = rentListingType,
                    rent = Money.of("2400.00"),
                    dateFrom = LocalDate.parse("2024-07-18"),
                    dateTo = LocalDate.parse("2024-08-29"),
                    recordSource = "apartments",
                    zipCode = "99577",
                    msaCode = "11260",
                    unitSquareFootage = BigDecimal("544"),
                    bedroomsQuantity = 1,
                    bathroomsQuantity = BigDecimal("1"),
                    floorPlan = "1x1 Lake Street",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        assertNotNull(result)
        assertEquals(expectedResult, result)

        result.forEach {
            val effectiveData = effectiveRentRepository.getByListingId(it.id)
            assertEquals(7, effectiveData.size)
        }
    }

    @Test
    fun `should consume real msj`() {
        val request =
            SendMessageRequest
                .builder()
                .queueUrl(queueUrl)
                .messageBody(ONE_MESSAGE)
                .build()

        client.sendMessage(request).get()
        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf("USCT-002368"),
                            type = RentListingType.FLOOR_PLAN,
                            typeId = "2BR/1.0BA",
                            dateFrom = LocalDate.parse("2025-01-01"),
                            dateTo = LocalDate.parse("2025-06-15"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }
        assertTrue(result.size > 1)
    }

    @AfterEach
    fun tearDown() {
        cleanQueue(client, queueUrl)
    }

    @Test
    fun `should keep last unit sft`() {
        val listing =
            MockedEntityFactory.buildRentListing(
                unitSquareFootage = BigDecimal("710"),
                dateFrom = LocalDate.parse("2023-10-23"),
                dateTo = LocalDate.parse("2023-10-25"),
            )

        val effectiveRent =
            listOf(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                ),
            )
        listingsRepository.save(listOf(listing))
        effectiveRentRepository.save(effectiveRent)

        val message =
            GroupedRentListingMessage(
                propertyId = listing.propertyId,
                type = listing.type,
                unitId = listing.typeId,
                zipCode = listing.zipCode,
                msaCode = listing.msaCode,
                unitSquareFootage = BigDecimal("510"),
                bedroomsQuantity = listing.bedroomsQuantity,
                bathroomsQuantity = listing.bathroomsQuantity,
                floorPlan = listing.floorPlan,
                records =
                    listOf(
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-23"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-24"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-25"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-26"),
                        ),
                        RentUnitListingMessage(
                            recordSource = "rent",
                            rent = BigDecimal("1540.00"),
                            rentDeposit = null,
                            effectiveRent = BigDecimal("1340.00"),
                            effectiveRentDeposit = null,
                            availableIn = null,
                            recordDate = LocalDate.parse("2024-07-27"),
                        ),
                    ),
            )

        sendOversizeMessage(client, s3Client, queueUrl, message)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    listingsRepository.findListingsForProperties(
                        PropertiesListingsQuery(
                            propertyIds = setOf(listing.propertyId),
                            type = listing.type,
                            typeId = listing.typeId,
                            dateFrom = LocalDate.parse("2024-07-22"),
                            dateTo = LocalDate.parse("2024-09-01"),
                            bathrooms = null,
                            bedrooms = null,
                            floorPlan = null,
                        ),
                    )
                }
            }

        assertNotNull(result)
        assertEquals(1, result.size)
        assertEquals(BigDecimal("510"), result.first().unitSquareFootage)
        effectiveRentRepository.getByListingId(result.first().id).forEach {
            assertEquals(BigDecimal("510"), it.unitSquareFootage)
        }
    }

    private fun setupExistingListings(
        listings: List<RentListing>,
        effectiveRents: List<EffectiveRent>,
    ) {
        listingsRepository.save(listings)
        effectiveRentRepository.save(effectiveRents)
    }
}

package integration.handlers.consumers

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.units.PropertyUnitMessage
import com.keyway.adapters.dtos.units.UnitMessage
import com.keyway.application.koin.ModuleConstants
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.sqs.IMessageHandler
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.core.qualifier.named
import org.koin.test.inject
import java.math.BigDecimal
import kotlin.test.assertTrue

class SavePropertyUnitsDataTest : BaseApplicationTest() {
    private val propertyUnitRepository: PropertyUnitRepository by inject()

    private val target: IMessageHandler by inject(
        named(ModuleConstants.PROPERTY_UNITS_DATA_HANDLER),
    )

    private fun sqsMessage(message: Any) =
        software.amazon.awssdk.services.sqs.model.Message
            .builder()
            .body(JsonMapper.encode(message))
            .build()

    private fun getPropUnitMessage(propertyId: String) =
        PropertyUnitMessage(
            propertyId = propertyId,
            units =
                listOf(
                    UnitMessage(
                        id = "101",
                        squareFootage = BigDecimal.TEN,
                        bedrooms = 1,
                        bathrooms = BigDecimal.ONE,
                        floorPlan = null,
                        amenities = emptySet(),
                        renovationProbability = null,
                        renovated = null,
                    ),
                    UnitMessage(
                        id = "102",
                        squareFootage = BigDecimal.TEN,
                        bedrooms = 1,
                        bathrooms = BigDecimal.ONE,
                        floorPlan = "CILINDRO",
                        amenities = setOf("PASION"),
                        renovationProbability = BigDecimal(0.7),
                        renovated = true,
                    ),
                ),
        )

    @Test
    fun `should save property units data`() {
        val propertyId = "USFL-014695"
        val message = getPropUnitMessage(propertyId)
        val result = target.processMessage(sqsMessage(message = message))
        assertTrue(result)

        val units = propertyUnitRepository.findByPropertyId(propertyId)

        assertEquals(2, units.size)
        assertEquals(1, units.count { it.renovated == true })
        assertEquals(2, units.count { setOf("101", "102").contains(it.unitId) })
    }

    @Test
    fun `should update property units data`() {
        val propertyId = "USFL-014695"
        val message = getPropUnitMessage(propertyId)
        val result = target.processMessage(sqsMessage(message = message))
        assertTrue(result)
        val messageUpdate =
            message.copy(
                units =
                    listOf(
                        UnitMessage(
                            id = "101",
                            squareFootage = BigDecimal.TEN,
                            bedrooms = 1,
                            bathrooms = BigDecimal.ONE,
                            floorPlan = "AA",
                            amenities = emptySet(),
                            renovationProbability = BigDecimal(0.5),
                            renovated = null,
                        ),
                        UnitMessage(
                            id = "102",
                            squareFootage = BigDecimal.TEN,
                            bedrooms = 1,
                            bathrooms = BigDecimal.ONE,
                            floorPlan = "CC",
                            amenities = setOf("NONE"),
                            renovationProbability = BigDecimal(0.9),
                            renovated = true,
                        ),
                    ),
            )
        val resultUpdate = target.processMessage(sqsMessage(message = messageUpdate))
        assertTrue(resultUpdate)

        val units = propertyUnitRepository.findByPropertyId(propertyId)
        assertEquals(2, units.size)
        assertEquals(1, units.count { it.renovated == true })
        assertEquals(2, units.count { setOf("101", "102").contains(it.unitId) })

        assertEquals(units.first { it.unitId == "101" }.floorPlan, "AA")
        assertEquals(units.first { it.unitId == "101" }.renovationProbability, BigDecimal(0.5))

        assertEquals(units.first { it.unitId == "102" }.floorPlan, "CC")
        assertEquals(units.first { it.unitId == "102" }.renovationProbability, BigDecimal(0.9))
    }
}

package integration.handlers.consumers

import application.utils.base.BaseApplicationTest
import com.keyway.application.configuration.model.Configuration
import com.keyway.application.configuration.model.findQueueConfiguration
import com.keyway.core.dto.concessions.input.ConcessionRecordV2Input
import com.keyway.core.dto.concessions.input.SaveConcessionV2Input
import com.keyway.core.entities.concessions.PropertyConcessionBenefit
import com.keyway.core.entities.concessions.PropertyConcessionV2
import com.keyway.core.ports.repositories.PropertyConcessionV2Repository
import com.keyway.kommons.aws.config.SqsConfig
import com.keyway.kommons.sqs.buildSqsClient
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.koin.test.get
import org.koin.test.inject
import utils.DateHelper
import utils.SqsHelpers.buildLocalstackQueueUrl
import utils.SqsHelpers.cleanQueue
import utils.SqsHelpers.receiveMessages
import utils.SqsHelpers.sendMessage
import utils.SqsHelpers.start
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.time.OffsetDateTime

class SaveConcessionV2IntegrationTest : BaseApplicationTest() {
    // TODO: WE NEED TO MOVE THIS BLOCK TO BASE APPLICATION TEST
    companion object {
        @JvmStatic
        @BeforeAll
        fun startConsumers() {
            consumers.start()
        }
    }

    private val config: Configuration by inject()
    private val client = buildSqsClient(config.awsConfig)
    private val queueName = get<SqsConfig>().findQueueConfiguration("property_concessions_v2").queueName
    private val queueUrl = buildLocalstackQueueUrl(queueName)

    private val concessionsRepository: PropertyConcessionV2Repository by inject()

    @AfterEach
    fun tearDown() {
        cleanQueue(client, queueUrl)
    }

    @Test
    fun `should consume the event and create a concession`() {
        DateHelper.setClockUTC(Instant.parse("2024-06-01T00:00:00Z"))
        val propertyId = "USFL-014695"
        val date = LocalDate.parse("2024-06-01")

        val message =
            SaveConcessionV2Input(
                propertyId = propertyId,
                zipCode = null,
                msaCode = null,
                records =
                    listOf(
                        ConcessionRecordV2Input(
                            concessionText = "15% discount on rent for the first 6 months",
                            recordDate = date,
                            benefits = emptyList(),
                        ),
                    ),
            )

        val concessionId = "706c5840-8773-4aaa-bef4-143bedb41111"
        idGenerator.setId(concessionId)
        sendMessage(client, queueUrl, message, needsSnsWrapping = false)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    concessionsRepository.findMostRecentConcessionByPropertyId(
                        propertyId,
                        LocalDate.parse("2024-06-02"),
                    )
                }
            }

        val expectedEntity =
            PropertyConcessionV2(
                id = concessionId,
                propertyId = "USFL-014695",
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits = emptyList(),
                createdAt = OffsetDateTime.parse("2024-06-01T00:00:00+00:00"),
                updatedAt = OffsetDateTime.parse("2024-06-01T00:00:00+00:00"),
                zipCode = null,
                msaCode = null,
            )

        assertNotNull(result)
        assertEquals(expectedEntity, result)
    }

    @Test
    fun `should consume the event and update a concession`() {
        DateHelper.setClockUTC(Instant.parse("2024-06-02T00:00:00Z"))
        val mostRecentId = "73e177ed-3d48-4639-9825-f1b16f0e2222"
        val propertyId = "USFL-014695"
        val date = LocalDate.parse("2024-06-02")

        val mostRecentConcession =
            PropertyConcessionV2(
                id = mostRecentId,
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("10.00"),
                            conditionBedrooms = listOf(1, 2),
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            )

        concessionsRepository.saveOrUpdate(mostRecentConcession)

        val message =
            SaveConcessionV2Input(
                propertyId = propertyId,
                zipCode = null,
                msaCode = null,
                records =
                    listOf(
                        ConcessionRecordV2Input(
                            concessionText = "15% discount on rent for the first 6 months",
                            recordDate = date,
                            benefits =
                                listOf(
                                    PropertyConcessionBenefit(
                                        freeMonthsAmount = BigDecimal("10.00"),
                                        conditionBedrooms = listOf(1, 2),
                                        oneTimeDollarsOffAmount = BigDecimal("10.00"),
                                        benefitGroupId = "123",
                                    ),
                                ),
                        ),
                    ),
            )

        sendMessage(client, queueUrl, message, needsSnsWrapping = false)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    concessionsRepository.findMostRecentConcessionByPropertyId(
                        propertyId,
                        LocalDate.parse("2024-06-03"),
                    )
                }
            }

        val expectedEntity =
            PropertyConcessionV2(
                id = mostRecentId,
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-02"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("10.00"),
                            conditionBedrooms = listOf(1, 2),
                            oneTimeDollarsOffAmount = BigDecimal("10.00"),
                            benefitGroupId = "123",
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            )

        assertNotNull(result)
        assertEquals(expectedEntity, result)
    }

    @Test
    fun `should consume the event and update a concession with msa and zip`() {
        DateHelper.setClockUTC(Instant.parse("2024-06-02T00:00:00Z"))
        val mostRecentId = "73e177ed-3d48-4639-9825-f1b16f0e2222"
        val propertyId = "USFL-014695"
        val date = LocalDate.parse("2024-06-02")

        val mostRecentConcession =
            PropertyConcessionV2(
                id = mostRecentId,
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-01"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("10.00"),
                            conditionBedrooms = listOf(1, 2),
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                zipCode = null,
                msaCode = null,
            )

        concessionsRepository.saveOrUpdate(mostRecentConcession)

        val message =
            SaveConcessionV2Input(
                propertyId = propertyId,
                zipCode = "77008",
                msaCode = "77008",
                records =
                    listOf(
                        ConcessionRecordV2Input(
                            concessionText = "15% discount on rent for the first 6 months",
                            recordDate = date,
                            benefits =
                                listOf(
                                    PropertyConcessionBenefit(
                                        freeMonthsAmount = BigDecimal("10.00"),
                                        conditionBedrooms = listOf(1, 2),
                                        oneTimeDollarsOffAmount = BigDecimal("10.00"),
                                        benefitGroupId = "123",
                                    ),
                                ),
                        ),
                    ),
            )

        sendMessage(client, queueUrl, message, needsSnsWrapping = false)

        val result =
            consumers.receiveMessages(client, queueUrl) {
                runBlocking {
                    concessionsRepository.findMostRecentConcessionByPropertyId(
                        propertyId,
                        LocalDate.parse("2024-06-03"),
                    )
                }
            }

        val expectedEntity =
            PropertyConcessionV2(
                id = mostRecentId,
                propertyId = propertyId,
                dateFrom = LocalDate.parse("2024-06-01"),
                dateTo = LocalDate.parse("2024-06-02"),
                concessionText = "15% discount on rent for the first 6 months",
                benefits =
                    listOf(
                        PropertyConcessionBenefit(
                            freeMonthsAmount = BigDecimal("10.00"),
                            conditionBedrooms = listOf(1, 2),
                            oneTimeDollarsOffAmount = BigDecimal("10.00"),
                            benefitGroupId = "123",
                        ),
                    ),
                createdAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                updatedAt = OffsetDateTime.parse("2024-06-02T00:00:00Z"),
                zipCode = "77008",
                msaCode = "77008",
            )

        assertNotNull(result)
        assertEquals(expectedEntity, result)
    }
}

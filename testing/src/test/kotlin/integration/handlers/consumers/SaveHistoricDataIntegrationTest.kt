package integration.handlers.consumers

import application.utils.base.BaseApplicationTest
import com.keyway.adapters.dtos.listings.GroupedRentListingMessage
import com.keyway.adapters.dtos.listings.RentUnitListingMessage
import com.keyway.adapters.mappers.SqsMapper
import com.keyway.adapters.repositories.model.RentListingDBModel
import com.keyway.application.koin.ModuleConstants
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import com.keyway.core.utils.DateUtils.toEndOfMonth
import com.keyway.kommons.db.SqlClient
import com.keyway.kommons.sqs.IMessageHandler
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.koin.core.qualifier.named
import org.koin.test.inject
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertTrue

class SaveHistoricDataIntegrationTest : BaseApplicationTest() {
    private val listingsRepository: ListingsRepository by inject()
    private val sqlClient: SqlClient by inject()

    private val saveGroupedListingHandler: IMessageHandler by inject(
        named(ModuleConstants.GROUPED_UNIT_RENT_DATA_HANDLER),
    )

    @Test
    fun `should consume the event and create the historic data`() {
        val message = createAndSendMessage(propertyId = "USFL-014695")

        val eventResult =
            saveGroupedListingHandler.processMessage(
                software.amazon.awssdk.services.sqs.model.Message
                    .builder()
                    .body(SqsMapper.encode(message))
                    .build(),
            )

        assertTrue(eventResult)

        val result = getResult()

        assertEquals(1, result.size)
        val record = message.records.first()
        val listing = result.first()

        assertEquals(listing.recordSource, record.recordSource)
        assertEquals(listing.dateFrom, record.recordDate)
        assertEquals(listing.dateTo, record.recordDate.toEndOfMonth())
        assertEquals(listing.rent, record.rent)
        assertEquals(listing.rentDeposit, record.rentDeposit)
    }

    @Test
    fun `should consume the event and dont create data because of existing listing`() {
        val propertyId = "USFL-014695"
        val message = createAndSendMessage(propertyId)
        listingsRepository.save(
            RentListing(
                id = "anyString",
                propertyId = propertyId,
                typeId = message.unitId,
                type = RentListingType.UNIT,
                rent = Money.of("1725.00"),
                dateFrom =
                    message.records
                        .first()
                        .recordDate
                        .plusDays(1),
                dateTo =
                    message.records
                        .first()
                        .recordDate
                        .plusDays(10),
                recordSource = "apartments",
                zipCode = "99577",
                msaCode = "11260",
                unitSquareFootage = BigDecimal("544"),
                bedroomsQuantity = 1,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "1x1 Lake Street",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
        )

        val eventResult =
            saveGroupedListingHandler.processMessage(
                software.amazon.awssdk.services.sqs.model.Message
                    .builder()
                    .body(SqsMapper.encode(message))
                    .build(),
            )

        assertTrue(eventResult)

        val result = getResult()
        assertEquals(0, result.size)
    }

    private fun getResult() =
        sqlClient
            .getAll(
                query = "SELECT * FROM historic_rent_listing",
                params = emptyList(),
                clazz = RentListingDBModel::class.java,
            ).map { it.toUnitRentListing() }

    private fun createAndSendMessage(
        propertyId: String,
        records: List<RentUnitListingMessage> =
            listOf(
                RentUnitListingMessage(
                    recordSource = "H-Y",
                    rent = BigDecimal("1000.00"),
                    rentDeposit = null,
                    effectiveRent = BigDecimal("1000.00"),
                    effectiveRentDeposit = null,
                    availableIn = null,
                    recordDate = LocalDate.parse("2024-01-01"),
                ),
            ),
    ): GroupedRentListingMessage =
        GroupedRentListingMessage(
            propertyId = propertyId,
            type = RentListingType.UNIT,
            unitId = "101",
            zipCode = "33009",
            msaCode = "35620",
            unitSquareFootage = BigDecimal("1119"),
            bedroomsQuantity = 2,
            bathroomsQuantity = BigDecimal("1"),
            floorPlan = "A2",
            records = records,
        )
}

package integration.handlers.utils

import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.utils.DateUtils
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

object DataUtils {
    val unitRentData =
        listOf(
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "141",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022222",
                type = RentListingType.UNIT,
                typeId = "222",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2024-10-17"),
                dateTo = LocalDate.parse("2024-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
        )

    val listingsData =
        listOf(
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "141",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-07"),
                dateTo = LocalDate.parse("2023-08-10"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-10-14"),
                dateTo = LocalDate.parse("2023-10-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1200),
                dateFrom = LocalDate.parse("2023-11-14"),
                dateTo = LocalDate.parse("2023-11-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(999),
                dateFrom = LocalDate.parse("2023-10-05"),
                dateTo = LocalDate.parse("2023-10-07"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "225",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022222",
                type = RentListingType.UNIT,
                typeId = "222",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2024-10-17"),
                dateTo = LocalDate.parse("2024-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USGA-004090",
                type = RentListingType.FLOOR_PLAN,
                typeId = "C1",
                rent = Money.of(1310),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-10"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("550"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "C1",
                availableIn = null,
                rentDeposit = Money.of(800),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USGA-004090",
                type = RentListingType.FLOOR_PLAN,
                typeId = "C2",
                rent = Money.of(1300),
                dateFrom = LocalDate.parse("2023-08-10"),
                dateTo = LocalDate.parse("2023-08-12"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("795"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "C2",
                availableIn = null,
                rentDeposit = Money.of(500),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USGA-004090",
                type = RentListingType.UNIT,
                typeId = "C 216",
                rent = Money.of(1100),
                dateFrom = LocalDate.parse("2023-08-10"),
                dateTo = LocalDate.parse("2023-08-12"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("795"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1"),
                floorPlan = "C2",
                availableIn = null,
                rentDeposit = Money.of(350),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
        )

    val multipleListingsData =
        listOf(
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "141",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022726",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-08-07"),
                dateTo = LocalDate.parse("2023-08-10"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022726",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-10-14"),
                dateTo = LocalDate.parse("2023-10-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022726",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1200),
                dateFrom = LocalDate.parse("2023-11-14"),
                dateTo = LocalDate.parse("2023-11-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027329",
                type = RentListingType.UNIT,
                typeId = "289",
                rent = Money.of(999),
                dateFrom = LocalDate.parse("2023-10-05"),
                dateTo = LocalDate.parse("2023-10-07"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027329",
                type = RentListingType.UNIT,
                typeId = "289",
                rent = Money.of(1091),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027777",
                type = RentListingType.UNIT,
                typeId = "225",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022222",
                type = RentListingType.UNIT,
                typeId = "222",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2024-10-17"),
                dateTo = LocalDate.parse("2024-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
        )

    val realisticCaseUnitsData =
        listOf(
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1000),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("400"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "104",
                rent = Money.of(1100),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("400"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "141",
                rent = Money.of(1050),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = LocalDate.parse("2023-08-04"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("450"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A21",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1200),
                dateFrom = LocalDate.parse("2023-08-07"),
                dateTo = LocalDate.parse("2023-08-10"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("550"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(200),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1150),
                dateFrom = LocalDate.parse("2023-10-14"),
                dateTo = LocalDate.parse("2023-10-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("550"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(700),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027626",
                type = RentListingType.UNIT,
                typeId = "236",
                rent = Money.of(1300),
                dateFrom = LocalDate.parse("2023-11-14"),
                dateTo = LocalDate.parse("2023-11-23"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("550"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(800),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027329",
                type = RentListingType.UNIT,
                typeId = "289",
                rent = Money.of(999),
                dateFrom = LocalDate.parse("2023-10-05"),
                dateTo = LocalDate.parse("2023-10-07"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("2"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = Money.of(600),
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-027329",
                type = RentListingType.UNIT,
                typeId = "289",
                rent = Money.of(1400),
                dateFrom = LocalDate.parse("2023-09-29"),
                dateTo = LocalDate.parse("2023-09-29"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 3,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A3",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022222",
                type = RentListingType.UNIT,
                typeId = "225",
                rent = Money.of(1093),
                dateFrom = LocalDate.parse("2023-10-17"),
                dateTo = LocalDate.parse("2023-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
            RentListing(
                id = UUID.randomUUID().toString(),
                propertyId = "USTX-022222",
                type = RentListingType.UNIT,
                typeId = "222",
                rent = Money.of(900),
                dateFrom = LocalDate.parse("2024-10-17"),
                dateTo = LocalDate.parse("2024-10-17"),
                recordSource = "apartments",
                zipCode = "75217",
                msaCode = "19100",
                unitSquareFootage = BigDecimal("895"),
                bedroomsQuantity = 2,
                bathroomsQuantity = BigDecimal("1.5"),
                floorPlan = "A2",
                availableIn = null,
                rentDeposit = null,
                createdAt = DateUtils.now(),
                updateAt = DateUtils.now(),
            ),
        )
}

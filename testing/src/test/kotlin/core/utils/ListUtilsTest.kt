package core.utils

import com.keyway.core.utils.distributeEvenly
import com.keyway.core.utils.distributeEvenlyInMaxPartitions
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class ListUtilsTest {
    @Test
    fun `distributeEvenly should handle empty collection`() {
        val emptyList = emptyList<Int>()
        val result = emptyList.distributeEvenly()

        assertEquals(1, result.size)
        assertEquals(emptyList, result[0])
    }

    @Test
    fun `distributeEvenly should handle single item collection`() {
        val singleItemList = listOf(1)
        val result = singleItemList.distributeEvenly()

        assertEquals(1, result.size)
        assertEquals(singleItemList, result[0])
    }

    @Test
    fun `distributeEvenly should distribute items evenly with default maxItemsPerPartition`() {
        val items = (1..10).toList()
        val result = items.distributeEvenly() // default maxItemsPerPartition = 5

        assertEquals(2, result.size)
        assertEquals(listOf(1, 2, 3, 4, 5), result[0])
        assertEquals(listOf(6, 7, 8, 9, 10), result[1])
    }

    @Test
    fun `distributeEvenly should distribute items with custom maxItemsPerPartition`() {
        val items = (1..12).toList()
        val result = items.distributeEvenly(maxItemsPerPartition = 3)

        assertEquals(4, result.size)
        assertEquals(listOf(1, 2, 3), result[0])
        assertEquals(listOf(4, 5, 6), result[1])
        assertEquals(listOf(7, 8, 9), result[2])
        assertEquals(listOf(10, 11, 12), result[3])
    }

    @Test
    fun `distributeEvenly should handle uneven distribution`() {
        val items = (1..7).toList()
        val result = items.distributeEvenly(maxItemsPerPartition = 3)

        assertEquals(3, result.size)
        assertEquals(listOf(1, 2, 3), result[0])
        assertEquals(listOf(4, 5, 6), result[1])
        assertEquals(listOf(7), result[2])
    }

    @Test
    fun `distributeEvenly should handle case where items fit in single partition`() {
        val items = (1..3).toList()
        val result = items.distributeEvenly(maxItemsPerPartition = 5)

        assertEquals(1, result.size)
        assertEquals(items, result[0])
    }

    @Test
    fun `distributeEvenly should throw exception for non-positive maxItemsPerPartition`() {
        val items = listOf(1, 2, 3)

        assertThrows<IllegalArgumentException> {
            items.distributeEvenly(maxItemsPerPartition = 0)
        }

        assertThrows<IllegalArgumentException> {
            items.distributeEvenly(maxItemsPerPartition = -1)
        }
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should handle empty collection`() {
        val emptyList = emptyList<Int>()
        val result = emptyList.distributeEvenlyInMaxPartitions()

        assertEquals(1, result.size)
        assertEquals(emptyList, result[0])
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should handle single item collection`() {
        val singleItemList = listOf(1)
        val result = singleItemList.distributeEvenlyInMaxPartitions()

        assertEquals(1, result.size)
        assertEquals(singleItemList, result[0])
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should distribute items evenly with default maxPartitions`() {
        val items = (1..12).toList()
        val result = items.distributeEvenlyInMaxPartitions() // default maxPartitions = 6

        assertEquals(6, result.size)
        assertEquals(listOf(1, 2), result[0])
        assertEquals(listOf(3, 4), result[1])
        assertEquals(listOf(5, 6), result[2])
        assertEquals(listOf(7, 8), result[3])
        assertEquals(listOf(9, 10), result[4])
        assertEquals(listOf(11, 12), result[5])
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should distribute items with custom maxPartitions`() {
        val items = (1..10).toList()
        val result = items.distributeEvenlyInMaxPartitions(maxPartitions = 3)

        assertEquals(3, result.size)
        assertEquals(listOf(1, 2, 3, 4), result[0]) // 4 items (10/3 = 3 + 1 remainder)
        assertEquals(listOf(5, 6, 7), result[1]) // 3 items
        assertEquals(listOf(8, 9, 10), result[2]) // 3 items
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should handle uneven distribution with remainder`() {
        val items = (1..11).toList()
        val result = items.distributeEvenlyInMaxPartitions(maxPartitions = 4)

        assertEquals(4, result.size)
        assertEquals(listOf(1, 2, 3), result[0]) // 3 items (11/4 = 2 + 3 remainder, first 3 partitions get +1)
        assertEquals(listOf(4, 5, 6), result[1]) // 3 items
        assertEquals(listOf(7, 8, 9), result[2]) // 3 items
        assertEquals(listOf(10, 11), result[3]) // 2 items
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should limit partitions to collection size`() {
        val items = (1..3).toList()
        val result = items.distributeEvenlyInMaxPartitions(maxPartitions = 10)

        assertEquals(3, result.size) // Limited to collection size
        assertEquals(listOf(1), result[0])
        assertEquals(listOf(2), result[1])
        assertEquals(listOf(3), result[2])
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should handle large collections`() {
        val items = (1..100).toList()
        val result = items.distributeEvenlyInMaxPartitions(maxPartitions = 7)

        assertEquals(7, result.size)
        // 100/7 = 14 with remainder 2, so first 2 partitions get 15 items, rest get 14
        assertEquals(15, result[0].size)
        assertEquals(15, result[1].size)
        assertEquals(14, result[2].size)
        assertEquals(14, result[3].size)
        assertEquals(14, result[4].size)
        assertEquals(14, result[5].size)
        assertEquals(14, result[6].size)

        // Verify all items are included
        val allItems = result.flatten()
        assertEquals(items, allItems)
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should throw exception for non-positive maxPartitions`() {
        val items = listOf(1, 2, 3)

        assertThrows<IllegalArgumentException> {
            items.distributeEvenlyInMaxPartitions(maxPartitions = 0)
        }

        assertThrows<IllegalArgumentException> {
            items.distributeEvenlyInMaxPartitions(maxPartitions = -1)
        }
    }

    @Test
    fun `distributeEvenlyInMaxPartitions should work with different data types`() {
        val stringItems = listOf("a", "b", "c", "d", "e")
        val result = stringItems.distributeEvenlyInMaxPartitions(maxPartitions = 2)

        assertEquals(2, result.size)
        assertEquals(listOf("a", "b", "c"), result[0]) // 5/2 = 2 + 1 remainder
        assertEquals(listOf("d", "e"), result[1])
    }

    @Test
    fun `distributeEvenly should work with different data types`() {
        val stringItems = listOf("apple", "banana", "cherry", "date", "elderberry")
        val result = stringItems.distributeEvenly(maxItemsPerPartition = 2)

        assertEquals(3, result.size)
        assertEquals(listOf("apple", "banana"), result[0])
        assertEquals(listOf("cherry", "date"), result[1])
        assertEquals(listOf("elderberry"), result[2])
    }
}

package core.service

import com.keyway.core.entities.UnitListingData
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.service.similarity.GaussianUnitSimilarityCalculator
import com.keyway.core.service.similarity.SimilarUnit
import com.keyway.core.service.similarity.UnitSimilarity
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import utils.MockedEntityFactory
import java.math.BigDecimal
import kotlin.math.abs

class GaussianUnitSimilarityCalculatorTest {
    private val unitSimilarityCalculatorConfig = MockedEntityFactory.buildUnitSimilarityCalculatorConfig(similarityPercentageFilter = 0.01)

    @Test
    fun `should calculate similarity with gaussian`() {
        val baseUnit =
            PropertyUnit(
                unitId = "100",
                propertyId = "USFL-1111",
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            )
        val baseProperty = listOf(baseUnit)

        val equalUnit =
            PropertyUnit(
                unitId = "1",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("150"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool", "BBQ", "Parking"),
            )
        // LESS SQFT SAME UNIT MIX
        val lessSqft =
            PropertyUnit(
                unitId = "11",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            )
        // DIFFERENT RENO STATE
        val diffReno =
            PropertyUnit(
                unitId = "12",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("100"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "F1",
                renovated = true,
                renovationProbability = BigDecimal("0.99"),
                amenities = setOf("Pool"),
            )
        // BIGGER IN UNIT MIX AND SQFT
        val biggerUnitMixSqft =
            PropertyUnit(
                unitId = "2",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("200"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            )
        // BIGGER IN UNIT MIX BUT EQUALS IN SQFT
        val biggerUnitMixEqSqft =
            PropertyUnit(
                unitId = "22",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("100"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "F2",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            )
        val otherUnit =
            PropertyUnit(
                unitId = "3",
                propertyId = "USFL-2222",
                squareFootage = BigDecimal("300"),
                bedrooms = 3,
                bathrooms = BigDecimal("2.5"),
                floorPlan = "F3",
                renovated = false,
                renovationProbability = BigDecimal("0.11"),
                amenities = setOf("Pool"),
            )

        val compsProperties =
            listOf(
                equalUnit,
                lessSqft,
                diffReno,
                biggerUnitMixSqft,
                biggerUnitMixEqSqft,
                otherUnit,
            )

        val gaussianResult =
            GaussianUnitSimilarityCalculator(unitSimilarityCalculatorConfig).calculateUnitSimilarity(
                baseProperty.map {
                    UnitListingData(it, emptyList())
                },
                compsProperties.map {
                    UnitListingData(it, emptyList())
                },
            )

        val expectedResult =
            listOf(
                UnitSimilarity(
                    baseUnit,
                    similarUnits =
                        listOf(
                            SimilarUnit(
                                unit = equalUnit,
                                listings = emptyList(),
                                score = 1.0,
                            ),
                            SimilarUnit(
                                lessSqft,
                                listings = emptyList(),
                                score = 0.8904055716042445,
                            ),
                            SimilarUnit(
                                biggerUnitMixSqft,
                                listings = emptyList(),
                                score = 0.6547188577132956,
                            ),
                            SimilarUnit(
                                biggerUnitMixEqSqft,
                                listings = emptyList(),
                                score = 0.6547188577132956,
                            ),
                            SimilarUnit(
                                otherUnit,
                                listings = emptyList(),
                                score = 0.11794051148024821,
                            ),
                        ),
                ),
            )

        assertNotNull(gaussianResult)
        assertEquals(expectedResult.size, gaussianResult.size)
        assertSimilarityListsEqual(expectedResult, gaussianResult)
    }

    private fun assertSimilarityListsEqual(
        expectedResult: List<UnitSimilarity>,
        actualResult: List<UnitSimilarity>,
        delta: Double = 0.0001,
    ) {
        assertEquals(expectedResult.size, actualResult.size, "Lists have different sizes")

        expectedResult.zip(actualResult).forEach { (expected, actual) ->
            assertEquals(expected.baseUnit, actual.baseUnit)

            assertEquals(expected.similarUnits.size, actual.similarUnits.size, "Different number of similarity scores")

            expected.similarUnits.zip(actual.similarUnits).forEach { (expScore, actScore) ->
                assertEquals(expScore.unit, actScore.unit)
                assert(abs(expScore.score - actScore.score) <= delta) {
                    "Scores differ more than $delta: expected ${expScore.score}, actual ${actScore.score}"
                }
            }
        }
    }
}

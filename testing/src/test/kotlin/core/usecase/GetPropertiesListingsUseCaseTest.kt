package core.usecase

import com.keyway.core.dto.listings.input.GetPropertiesListingsInput
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.service.listing.GetPropertiesListingService
import com.keyway.core.usecases.listings.GetPropertiesListingsUseCase
import com.keyway.core.usecases.listings.ListingsWithEffectiveRentMapper
import com.keyway.core.utils.DateUtils
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertNotNull

class GetPropertiesListingsUseCaseTest {
    @Test
    fun `should execute use case successfully`() {
        val input =
            GetPropertiesListingsInput(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bedrooms = null,
                bathrooms = null,
            )

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val mockedResponse =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-022222",
                    type = RentListingType.UNIT,
                    typeId = "104",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-10-17"),
                    dateTo = LocalDate.parse("2023-10-17"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        val listingsRepository = mockk<GetPropertiesListingService>()
        val effectiveRentRepository = mockk<EffectiveRentRepository>()
        val listingsWithEffectiveRentMapper = ListingsWithEffectiveRentMapper(effectiveRentRepository)
        coEvery { listingsRepository.all(any()) } returns mockedResponse
        coEvery { effectiveRentRepository.getByListingIds(mockedResponse.map { it.id }, any(), any()) } returns emptyList()

        val useCase = GetPropertiesListingsUseCase(listingsRepository, listingsWithEffectiveRentMapper)

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
            assertNotNull(result)
        }
    }

    @Test
    fun `should execute the use case throwing an error`() {
        val input =
            GetPropertiesListingsInput(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bedrooms = null,
                bathrooms = null,
            )

        val listingsRepository: GetPropertiesListingService = mockk<GetPropertiesListingService>()
        val listingsWithEffectiveRentMapper = mockk<ListingsWithEffectiveRentMapper>()
        coEvery { listingsRepository.all(any()) } returns emptyList()

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))
        val useCase = GetPropertiesListingsUseCase(listingsRepository, listingsWithEffectiveRentMapper)

        assertThrows<NotFoundException> {
            runBlocking { useCase.execute(input) }
        }
    }
}

package core.usecase

import com.keyway.core.dto.listings.input.GetPropertyRentDataInput
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.usecases.listings.GetPropertyLastListingUseCase
import com.keyway.core.usecases.listings.ListingsWithEffectiveRentMapper
import com.keyway.core.utils.DateUtils
import io.mockk.coEvery
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertNotNull

class GetPropertyLastListingUseCaseTest {
    @Test
    fun `should execute the use case without error`() {
        // Given
        val input =
            GetPropertyRentDataInput(
                propertyId = "USTX-027626",
                dateFrom = null,
                dateTo = null,
            )

        val mockedResponse =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "104",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-10-17"),
                    dateTo = LocalDate.parse("2023-10-17"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ),
            )

        val listingsRepository = mockk<ListingsRepository>()
        val effectiveRentRepository = mockk<EffectiveRentRepository>()
        val listingsWithEffectiveRentMapper = ListingsWithEffectiveRentMapper(effectiveRentRepository)
        coEvery { listingsRepository.findLastListingByProperties(any()) } returns mockedResponse
        coEvery { effectiveRentRepository.getByListingIds(mockedResponse.map { it.id }, any(), any()) } returns emptyList()

        // When
        val useCase = GetPropertyLastListingUseCase(listingsRepository, listingsWithEffectiveRentMapper)

        assertDoesNotThrow {
            val result = useCase.execute(input)
            assertNotNull(result)
        }
    }

    @Test
    fun `should execute the use case throwing an error`() {
        // Given
        val input =
            GetPropertyRentDataInput(
                propertyId = "USTX-027626",
                dateFrom = null,
                dateTo = null,
            )

        val listingsRepository: ListingsRepository = mockk<ListingsRepository>()
        val listingsWithEffectiveRentMapper = mockk<ListingsWithEffectiveRentMapper>()
        coEvery { listingsRepository.findLastListingByProperties(any()) } returns emptyList()

        // When
        val useCase = GetPropertyLastListingUseCase(listingsRepository, listingsWithEffectiveRentMapper)

        // Then
        assertThrows<NotFoundException> {
            useCase.execute(input)
        }
    }
}

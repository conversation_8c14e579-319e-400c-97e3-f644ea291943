package core.usecase

import com.keyway.core.dto.Metric
import com.keyway.core.dto.MetricDto
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.input.ComputeAggregatedMetricInput
import com.keyway.core.dto.listings.output.BedroomMetricsOutput
import com.keyway.core.dto.listings.output.BedroomRentMetricOutput
import com.keyway.core.dto.listings.output.ByIdMetricsOutput
import com.keyway.core.dto.listings.output.ByIdRentMetricOutput
import com.keyway.core.dto.listings.output.FloorPlanMetricsOutput
import com.keyway.core.dto.listings.output.FloorPlanRentMetricOutput
import com.keyway.core.dto.listings.output.MetricDetailOutput
import com.keyway.core.dto.listings.output.UnitMixMetricsOutput
import com.keyway.core.dto.listings.output.UnitMixRentMetricsOutput
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.usecases.metrics.ComputeAggregatedMetricUseCase
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import kotlin.random.Random
import kotlin.test.assertIs

class ComputeAggregatedMetricUseCaseTest {
    @Test
    fun `should compute metric without error and return property type metrics`() {
        val input =
            ComputeAggregatedMetricInput(
                ids = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.BY_ID,
                unitCondition = null,
            )

        val mockedResponse = listOf(getMetricDto())

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns mockedResponse
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        val expectedResponse =
            listOf(
                ByIdMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            ByIdRentMetricOutput(
                                averageListingDays = BigDecimal("0.20"),
                                recordsQuantity = 10,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                        ),
                    type = AggregatedMetricType.BY_ID,
                ),
            )

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
            assertNotNull(result)
            assertIs<List<ByIdMetricsOutput>>(result)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun `should compute metric without error and return bedroom type metrics`() {
        val input =
            ComputeAggregatedMetricInput(
                ids = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.BEDROOMS,
                unitCondition = null,
            )

        val mockedResponse = listOf(getMetricDto(bedrooms = 2), getMetricDto(bedrooms = 3))

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns mockedResponse
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        val expectedResponse =
            listOf(
                BedroomMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            BedroomRentMetricOutput(
                                bedrooms = 2,
                                recordsQuantity = 10,
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                            BedroomRentMetricOutput(
                                bedrooms = 3,
                                recordsQuantity = 10,
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                        ),
                ),
            )

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
            assertNotNull(result)
            assertIs<List<BedroomMetricsOutput>>(result)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun `should compute metric without error and return unit mix type metrics`() {
        val input =
            ComputeAggregatedMetricInput(
                ids = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.UNIT_MIX,
                unitCondition = null,
            )

        val mockedResponse =
            listOf(getMetricDto(bedrooms = 2, bathrooms = BigDecimal("1.5")), getMetricDto(bedrooms = 3, bathrooms = BigDecimal("2.0")))

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns mockedResponse
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        val expectedResponse =
            listOf(
                UnitMixMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            UnitMixRentMetricsOutput(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                recordsQuantity = 10,
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                            UnitMixRentMetricsOutput(
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                recordsQuantity = 10,
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                        ),
                ),
            )

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
            assertNotNull(result)
            assertIs<List<UnitMixMetricsOutput>>(result)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun `should compute metric without error and return floor plan type metrics`() {
        val input =
            ComputeAggregatedMetricInput(
                ids = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.FLOOR_PLAN,
                unitCondition = null,
            )
        val mockedResponse =
            listOf(getMetricDto(bedrooms = 2, bathrooms = BigDecimal("1.5"), floorPlan = "A2"), getMetricDto(bedrooms = 3, bathrooms = BigDecimal("2.0"), floorPlan = "A3"))

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns mockedResponse
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        val expectedResponse =
            listOf(
                FloorPlanMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            FloorPlanRentMetricOutput(
                                floorPlan = "A2",
                                recordsQuantity = 10,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                            FloorPlanRentMetricOutput(
                                floorPlan = "A3",
                                recordsQuantity = 10,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2.0"),
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                askingRent = getMetricOutput(),
                                deposit = getMetricOutput(),
                                squareFootage = getMetricOutput(),
                                effectiveRent = getMetricOutput(),
                                effectiveRentPSF = getMetricOutput(BigDecimal("1.00")),
                                askingRentPSF = getMetricOutput(BigDecimal("1.00")),
                            ),
                        ),
                ),
            )

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
            assertNotNull(result)
            assertIs<List<FloorPlanMetricsOutput>>(result)
            assertEquals(expectedResponse, result)
        }
    }

    @Test
    fun `should execute the use case throwing an error`() {
        val input =
            ComputeAggregatedMetricInput(
                ids = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.FLOOR_PLAN,
                unitCondition = null,
            )

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns emptyList()
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        assertThrows<NotFoundException> {
            runBlocking { useCase.execute(input) }
        }
    }

    @Test
    fun `should chunk the input and call the metrics repository 5 times`() {
        val fakeIds =
            generateSequence {
                "USTX-${Random.nextInt(1000, 9999)}"
            }.take(45).toSet()

        val input =
            ComputeAggregatedMetricInput(
                ids = fakeIds,
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                idType = IdType.PROPERTY,
                type = AggregatedMetricType.BY_ID,
                unitCondition = null,
            )

        val mockedResponse = listOf(getMetricDto())

        DateHelper.setClockUTC(Instant.parse("2023-10-31T00:00:00Z"))

        val metricsRepository = mockk<MetricsRepository>()
        coEvery { metricsRepository.aggregateMetrics(any()) } returns mockedResponse
        val useCase = ComputeAggregatedMetricUseCase(metricsRepository)

        assertDoesNotThrow {
            val result = runBlocking { useCase.execute(input) }
        }
        coVerify(exactly = 45 / 5) { metricsRepository.aggregateMetrics(any()) }
    }

    private fun getMetricDto(
        propertyId: String = "USTX-027626",
        floorPlan: String? = null,
        bedrooms: Int? = null,
        bathrooms: BigDecimal? = null,
    ) = MetricDto(
        id = propertyId,
        totalRecords = 10,
        averageListingsDays = BigDecimal("0.20"),
        askingRent = getMetric(),
        deposit = getMetric(),
        effectiveRent = getMetric(),
        squareFootage = getMetric(),
        bedrooms = bedrooms,
        bathrooms = bathrooms,
        floorPlan = floorPlan,
        unitsAvailable = 0,
        totalUnits = 9,
        unitId = null,
    )

    private fun getMetric() = Metric(min = BigDecimal("600.00"), max = BigDecimal("600.00"), average = BigDecimal("600.00"), median = BigDecimal("600.00"))

    private fun getMetricOutput(defaultValue: BigDecimal = BigDecimal("600.00")) = MetricDetailOutput(min = defaultValue, max = defaultValue, average = defaultValue, median = defaultValue)
}

package utils

import com.keyway.adapters.mappers.SqsMapper
import com.keyway.kommons.sqs.SqsConsumer
import com.keyway.kommons.sqs.configuration.SqsQueueConfig
import com.keyway.kommons.sqs.processors.OversizeProcessor
import kotlinx.coroutines.future.await
import kotlinx.coroutines.runBlocking
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.model.PurgeQueueRequest
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.SendMessageRequest

object SqsHelpers {
    private fun createSqsMessage(
        messageBody: String,
        messageAttributes: Map<String, Map<String, Any>> = emptyMap(),
    ): String {
        val formattedBody = messageBody.formatAsJsonString()
        val attributesJson = messageAttributes.formatAsJsonAttributes()

        return """
            {
              "Type" : "Notification",
              "MessageId" : "056322cd-bfa8-5703-a285-7e8eddf08882",
              "TopicArn" : "arn:aws:sns:us-east-1:681574592108:dev-synchropy-demographics_stats",
              "Message": "$formattedBody",
              "Timestamp": "2023-01-19T15:22:00.123Z",
              "SignatureVersion": "1",
              "Signature": "ZFX44D4K3zzZUl0VblTPOasoQfuq4qD6HBaUNe2uGQr8LOouHZfPluW9HEiLF+cAeeApn6ASorAzbV0lduQWE4RYc1Nh30LMidbE3SBG4Ju5wcW2Ir4TRMwEVW58SA8zWqDKAOE+10RWU9O7wdXLjxWUgFOWkKAg0KQoJqJ/0bXBmzn8A5yRkEi9mBnkzIAb38n6/GGPkwQnynYPnzIOwy8FmtEStoj22jWcgxgxvTpTXNY/kc5a5IeF0DDIWEZpjXqlk+/CnSJmmWHFPV46BT1jjDmRqBf4wcCl7UECxfeCoeG3o/Qb4+dzmU9BbBxvu7OAg1n17E17KXsCMxyAaA==",
              "SigningCertURL": "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-56e67fcb41f6fec09b0196692625d385.pem",
              "UnsubscribeURL": "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:681574592108:dev-synchropy-demographics_stats:5ccb9878-98a7-476d-ac30-945b149afdcb",
              "MessageAttributes": $attributesJson
            }
            """.trimIndent()
    }

    fun sendMessage(
        client: SqsAsyncClient,
        queueUrl: String,
        message: Any,
        messageAttributes: Map<String, Map<String, Any>> = emptyMap(),
        needsSnsWrapping: Boolean = true,
    ) {
        val encodedMessage =
            when (needsSnsWrapping) {
                true -> createSqsMessage(SqsMapper.encode(message), messageAttributes)
                false -> SqsMapper.encode(message)
            }

        runBlocking {
            val request =
                SendMessageRequest
                    .builder()
                    .queueUrl(queueUrl)
                    .messageBody(encodedMessage)
                    .build()

            client.sendMessage(request).get()
        }
    }

    fun sendOversizeMessage(
        client: SqsAsyncClient,
        s3Client: S3Client,
        queueUrl: String,
        message: Any,
        messageAttributes: Map<String, Map<String, Any>> = emptyMap(),
    ) {
        runBlocking {
            val processor =
                OversizeProcessor(
                    s3Client = s3Client,
                    sqsQueueConfig = SqsQueueConfig.default(queueUrl),
                )

            val request =
                processor.writer(
                    queueName = queueUrl,
                    message = SqsMapper.encode(message),
                    messageAttributes = emptyMap(),
                )

            client.sendMessage(request).get()
        }
    }

    fun buildLocalstackQueueUrl(queueName: String): String = "http://localhost:4566/000000000000/$queueName"

    inline fun <R> Set<SqsConsumer>.receiveMessages(
        client: SqsAsyncClient,
        queueUrl: String,
        block: (Set<SqsConsumer>) -> R,
    ): R {
        runBlocking {
            while (true) {
                if (isQueueEmpty(client, queueUrl)) {
                    break
                }
            }
        }
        return block(this)
    }

    suspend fun isQueueEmpty(
        client: SqsAsyncClient,
        queueUrl: String,
    ): Boolean {
        val request =
            ReceiveMessageRequest
                .builder()
                .queueUrl(queueUrl)
                .maxNumberOfMessages(1)
                .waitTimeSeconds(1)
                .build()

        val response = client.receiveMessage(request).await()
        return response.messages().isEmpty()
    }

    fun Set<SqsConsumer>.start() = this.forEach { it.start() }

    fun Set<SqsConsumer>.shutdown() = this.forEach { it.stop() }

    private fun String.formatAsJsonString() = this.replace("\n", "").replace("\"", "\\\"")

    private fun Map<String, Map<String, Any>>.formatAsJsonAttributes() =
        takeIf { it.isNotEmpty() }
            ?.let { attributes ->
                attributes.entries.joinToString(separator = ",", prefix = "{", postfix = "}") { (key, value) ->
                    "\"$key\": {${value.toJsonInnerAttributes()}}"
                }
            } ?: "{}"

    private fun Map<String, Any>.toJsonInnerAttributes(): String =
        entries.joinToString(separator = ",") { (key, value) ->
            "\"$key\": ${value.toJsonValue()}"
        }

    private fun Any.toJsonValue(): String =
        when (this) {
            is String -> "\"$this\""
            is Number, is Boolean -> toString()
            else -> "\"${toString()}\""
        }

    fun cleanQueue(
        client: SqsAsyncClient,
        queueUrl: String,
    ) {
        runBlocking {
            val request =
                PurgeQueueRequest
                    .builder()
                    .queueUrl(queueUrl)
                    .build()

            client.purgeQueue(request).await()
        }
    }
}

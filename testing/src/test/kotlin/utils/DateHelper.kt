package utils

import application.utils.clock.FixedClock
import com.keyway.core.utils.DateUtils
import java.time.Instant
import java.time.ZoneId
import java.time.ZoneOffset.UTC

object DateHelper {
    fun setClockUTC(instant: Instant) {
        setClock(instant, UTC)
    }

    fun setClockUTC(instant: String) {
        setClock(instant, UTC)
    }

    fun setClock(
        instant: Instant,
        zone: ZoneId,
    ) {
        DateUtils.setClock(FixedClock(instant, zone))
    }

    fun setClock(
        instant: String,
        zone: ZoneId,
    ) {
        DateUtils.setClock(FixedClock(Instant.parse(instant), zone))
    }
}

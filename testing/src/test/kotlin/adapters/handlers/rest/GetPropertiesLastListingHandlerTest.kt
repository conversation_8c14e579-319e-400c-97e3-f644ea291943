package adapters.handlers.rest

import adapters.handlers.rest.RentListingExtensions.toEffectiveRentsOutput
import com.keyway.adapters.dtos.listings.PropertiesListingDataInput
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.exceptions.RestException
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.handlers.rest.units.GetPropertiesLastListingHandler
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.usecases.listings.GetPropertiesLastListingUseCase
import com.keyway.core.utils.DateUtils
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNotNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID

class GetPropertiesLastListingHandlerTest {
    @Test
    fun `should execute the use case successfully`() {
        DateHelper.setClockUTC(Instant.parse("2023-10-20T00:00:00Z"))
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626,USTX-022222"),
                dateFrom = null,
                dateTo = null,
                bedrooms = null,
                bathrooms = null,
            )

        val propertyOne =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val propertyTwo =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-022222",
                    type = RentListingType.UNIT,
                    typeId = "104",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-10-17"),
                    dateTo = LocalDate.parse("2023-10-17"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = LocalDate.parse("2023-10-27"),
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val mockedResponse =
            mapOf(
                "USTX-027626" to propertyOne,
                "USTX-022222" to propertyTwo,
            )

        val expectedResponse =
            listOf(
                RentListingsResponse(
                    propertyId = "USTX-027626",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "236",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
                RentListingsResponse(
                    propertyId = "USTX-022222",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "104",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-10-17"),
                                listingTo = LocalDate.parse("2023-10-17"),
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                availableOn = LocalDate.parse("2023-10-27"),
                                effectiveRents = listOf(),
                                active = true,
                            ),
                        ),
                ),
            )

        val useCase = mockk<GetPropertiesLastListingUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse

        val handler = GetPropertiesLastListingHandler(BaseUseCaseExecutor, useCase)
        val result = runBlocking { handler.invoke(input) }

        // Then
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should execute the use case successfully only with one id`() {
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bedrooms = null,
                bathrooms = null,
            )

        val propertyOne =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val mockedResponse =
            mapOf(
                "USTX-027626" to propertyOne,
            )

        val expectedResponse =
            listOf(
                RentListingsResponse(
                    propertyId = "USTX-027626",
                    units =
                        listOf(
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "141",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-08-03"),
                                listingTo = LocalDate.parse("2023-08-04"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = BigDecimal("600.00"),
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                            RentListingResponse(
                                rentListingType = RentListingType.UNIT,
                                unitId = "236",
                                floorPlan = "A2",
                                askingRent = BigDecimal("1091.00"),
                                listingFrom = LocalDate.parse("2023-09-29"),
                                listingTo = LocalDate.parse("2023-09-29"),
                                availableOn = null,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                deposit = null,
                                squareFootage = BigDecimal("895"),
                                effectiveRents = listOf(),
                                active = false,
                            ),
                        ),
                ),
            )

        val useCase = mockk<GetPropertiesLastListingUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse

        val handler = GetPropertiesLastListingHandler(BaseUseCaseExecutor, useCase)
        val result = runBlocking { handler.invoke(input) }

        // Then
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should convert exception into a error response`() {
        val input =
            PropertiesListingDataInput(
                propertiesIds = setOf("USTX-027626"),
                dateFrom = LocalDate.parse("2023-08-03"),
                dateTo = null,
                bedrooms = null,
                bathrooms = null,
            )
        val useCase = mockk<GetPropertiesLastListingUseCase>()
        coEvery { useCase.execute(any()) } throws NotFoundException("No listings data for the given properties: ${input.propertiesIds}")

        val handler = GetPropertiesLastListingHandler(BaseUseCaseExecutor, useCase)

        // Then
        val error = assertThrows<RestException> { runBlocking { handler.invoke(input) } }
        assertNotNull(error)
        assertEquals(error.message, "404 | NOT_FOUND - No listings data for the given properties: ${input.propertiesIds}")
        assertEquals(error.statusCode, 404)
        assertEquals(error.httpStatusCode, 404)
        assertEquals(error.errorCode, "NOT_FOUND")
    }
}

package adapters.handlers.rest

import com.keyway.core.dto.listings.output.RentListingWithEffectiveRentOutput
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.RentListing

object RentListingExtensions {
    fun RentListing.toEffectiveRentsOutput(effectiveRents: List<EffectiveRent> = emptyList()) =
        RentListingWithEffectiveRentOutput(
            rentListing = this,
            effectiveRents = effectiveRents,
        )
}

package adapters.handlers.rest

import adapters.handlers.rest.RentListingExtensions.toEffectiveRentsOutput
import com.keyway.adapters.dtos.listings.RentListingInput
import com.keyway.adapters.dtos.listings.RentListingResponse
import com.keyway.adapters.dtos.listings.RentListingsResponse
import com.keyway.adapters.exceptions.RestException
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.handlers.rest.units.GetPropertyLastListingHandler
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.exceptions.base.NotFoundException
import com.keyway.core.usecases.listings.GetPropertyLastListingUseCase
import com.keyway.core.utils.DateUtils
import io.kotest.matchers.collections.shouldContainAll
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import utils.DateHelper
import java.math.BigDecimal
import java.time.Instant
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class GetPropertyLastListingHandlerTest {
    @Test
    fun `should execute the use case successfully`() {
        DateHelper.setClockUTC(Instant.parse("2023-10-20T00:00:00Z"))
        // Given
        val input =
            RentListingInput(
                propertyId = "USTX-027626",
                dateFrom = null,
                dateTo = null,
            )

        val useCase: GetPropertyLastListingUseCase = mockk<GetPropertyLastListingUseCase>()

        val mockedResponse =
            listOf(
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "141",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-08-03"),
                    dateTo = LocalDate.parse("2023-08-04"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = Money.of(600),
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "236",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-09-29"),
                    dateTo = LocalDate.parse("2023-09-29"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
                RentListing(
                    id = UUID.randomUUID().toString(),
                    propertyId = "USTX-027626",
                    type = RentListingType.UNIT,
                    typeId = "104",
                    rent = Money.of(1091),
                    dateFrom = LocalDate.parse("2023-10-17"),
                    dateTo = LocalDate.parse("2023-10-17"),
                    recordSource = "apartments",
                    zipCode = "75217",
                    msaCode = "19100",
                    unitSquareFootage = BigDecimal("895"),
                    bedroomsQuantity = 2,
                    bathroomsQuantity = BigDecimal("1.5"),
                    floorPlan = "A2",
                    availableIn = null,
                    rentDeposit = null,
                    createdAt = DateUtils.now(),
                    updateAt = DateUtils.now(),
                ).toEffectiveRentsOutput(),
            )

        val expectedResponse =
            RentListingsResponse(
                propertyId = "USTX-027626",
                units =
                    listOf(
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "236",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-09-29"),
                            listingTo = LocalDate.parse("2023-09-29"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "141",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-08-03"),
                            listingTo = LocalDate.parse("2023-08-04"),
                            availableOn = null,
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = BigDecimal("600.00"),
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = false,
                        ),
                        RentListingResponse(
                            rentListingType = RentListingType.UNIT,
                            unitId = "104",
                            floorPlan = "A2",
                            askingRent = BigDecimal("1091.00"),
                            listingFrom = LocalDate.parse("2023-10-17"),
                            listingTo = LocalDate.parse("2023-10-17"),
                            availableOn = LocalDate.parse("2023-10-20"),
                            bedrooms = 2,
                            bathrooms = BigDecimal("1.5"),
                            deposit = null,
                            squareFootage = BigDecimal("895"),
                            effectiveRents = listOf(),
                            active = true,
                        ),
                    ),
            )

        // When
        every { useCase.execute(any()) } returns mockedResponse
        val handler = GetPropertyLastListingHandler(BaseUseCaseExecutor, useCase)
        val result = handler.invoke(input)

        // Then
        assertNotNull(result)
        result.units.shouldContainAll(expectedResponse.units)
    }

    @Test
    fun `should not execute the use case successfully`() {
        // Given
        val input =
            RentListingInput(
                propertyId = "USTX-027626",
                dateFrom = null,
                dateTo = null,
            )

        val useCase: GetPropertyLastListingUseCase = mockk<GetPropertyLastListingUseCase>()

        // When
        every { useCase.execute(any()) } throws NotFoundException("No unit rent data for propertyId: ${input.propertyId}")
        val handler = GetPropertyLastListingHandler(BaseUseCaseExecutor, useCase)

        // Then
        val exception = assertThrows<RestException> { handler.invoke(input) }
        assertNotNull(exception)
        assertEquals(exception.message, "404 | NOT_FOUND - No unit rent data for propertyId: ${input.propertyId}")
        assertEquals(exception.statusCode, 404)
        assertEquals(exception.httpStatusCode, 404)
        assertEquals(exception.errorCode, "NOT_FOUND")
    }
}

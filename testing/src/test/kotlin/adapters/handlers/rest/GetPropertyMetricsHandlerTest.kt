package adapters.handlers.rest

import com.keyway.adapters.dtos.metrics.MetricDetail
import com.keyway.adapters.dtos.metrics.property.BedroomRentMetric
import com.keyway.adapters.dtos.metrics.property.BedroomRentSummary
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentMetric
import com.keyway.adapters.dtos.metrics.property.FloorPlanRentSummary
import com.keyway.adapters.dtos.metrics.property.PropertyRentMetric
import com.keyway.adapters.dtos.metrics.property.PropertyRentSummary
import com.keyway.adapters.dtos.metrics.property.UnitMixRentMetric
import com.keyway.adapters.dtos.metrics.property.UnitMixRentSummary
import com.keyway.adapters.executor.BaseUseCaseExecutor
import com.keyway.adapters.handlers.rest.metrics.GetPropertyMetricsHandler
import com.keyway.application.mapper.AppMapperConfigs.lowerCamelCaseObjectMapper
import com.keyway.core.dto.listings.input.AggregatedMetricType
import com.keyway.core.dto.listings.output.BedroomMetricsOutput
import com.keyway.core.dto.listings.output.BedroomRentMetricOutput
import com.keyway.core.dto.listings.output.ByIdMetricsOutput
import com.keyway.core.dto.listings.output.ByIdRentMetricOutput
import com.keyway.core.dto.listings.output.FloorPlanMetricsOutput
import com.keyway.core.dto.listings.output.FloorPlanRentMetricOutput
import com.keyway.core.dto.listings.output.MetricDetailOutput
import com.keyway.core.dto.listings.output.UnitMixMetricsOutput
import com.keyway.core.dto.listings.output.UnitMixRentMetricsOutput
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.usecases.metrics.ComputeAggregatedMetricUseCase
import com.keyway.kommons.mapper.JsonMapper
import com.keyway.kommons.mapper.jackson.JacksonMapper
import io.mockk.coEvery
import io.mockk.mockk
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertNotNull

class GetPropertyMetricsHandlerTest {
    @BeforeEach
    fun setUp() {
        JsonMapper.setMapper(JacksonMapper(lowerCamelCaseObjectMapper))
    }

    @Test
    fun `should handle property request with success`() {
        val input =
            GetPropertyMetricsHandler.Input(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                metricType = MetricType.BY_ID,
                unitCondition = null,
            )

        val mockedResponse =
            listOf(
                ByIdMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            ByIdRentMetricOutput(
                                askingRent =
                                    getMetricDetailOutput(),
                                askingRentPSF =
                                    getMetricDetailOutput(),
                                effectiveRent =
                                    getMetricDetailOutput(),
                                effectiveRentPSF =
                                    getMetricDetailOutput(),
                                deposit =
                                    getMetricDetailOutput(),
                                squareFootage =
                                    getMetricDetailOutput(),
                                averageListingDays = BigDecimal("2.67"),
                                recordsQuantity = 9,
                                unitsAvailable = 0,
                                totalUnits = 9,
                            ),
                        ),
                    type = AggregatedMetricType.BY_ID,
                ),
            )

        val useCase = mockk<ComputeAggregatedMetricUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse
        val handler = GetPropertyMetricsHandler(BaseUseCaseExecutor, useCase)

        val expectedResponse =
            listOf(
                PropertyRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            PropertyRentMetric(
                                deposit =
                                    getMetricDetail(),
                                squareFootage =
                                    getMetricDetail(),
                                averageListingDays = BigDecimal("2.67"),
                                recordsQuantity = 9,
                                unitsAvailable = 0,
                                askingRent =
                                    getMetricDetail(),
                                askingRentPSF =
                                    getMetricDetail(),
                                effectiveRent =
                                    getMetricDetail(),
                                effectiveRentPSF =
                                    getMetricDetail(),
                                totalUnits = 9,
                            ),
                        ),
                ),
            )

        val result = runBlocking { handler.invoke(input) }
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should handle bedroom request with success`() {
        val input =
            GetPropertyMetricsHandler.Input(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                metricType = MetricType.BEDROOMS,
                unitCondition = null,
            )

        val mockedResponse =
            listOf(
                BedroomMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            BedroomRentMetricOutput(
                                bedrooms = 2,
                                recordsQuantity = 5,
                                averageListingDays = BigDecimal("0.20"),
                                unitsAvailable = 0,
                                askingRent = getMetricDetailOutput(),
                                deposit = getMetricDetailOutput(),
                                squareFootage = getMetricDetailOutput(),
                                effectiveRent = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                totalUnits = 9,
                            ),
                            BedroomRentMetricOutput(
                                bedrooms = 3,
                                recordsQuantity = 4,
                                averageListingDays = BigDecimal("5.75"),
                                unitsAvailable = 0,
                                askingRent = getMetricDetailOutput(),
                                deposit = getMetricDetailOutput(),
                                squareFootage = getMetricDetailOutput(),
                                effectiveRent = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                totalUnits = 9,
                            ),
                        ),
                ),
            )

        val useCase = mockk<ComputeAggregatedMetricUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse
        val handler = GetPropertyMetricsHandler(BaseUseCaseExecutor, useCase)

        val expectedResponse =
            listOf(
                BedroomRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            BedroomRentMetric(
                                bedrooms = 2,
                                askingRent = getMetricDetail(),
                                deposit = getMetricDetail(),
                                averageListingDays = BigDecimal("0.20"),
                                recordsQuantity = 5,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                                squareFootage = getMetricDetail(),
                            ),
                            BedroomRentMetric(
                                bedrooms = 3,
                                askingRent = getMetricDetail(),
                                deposit = getMetricDetail(),
                                averageListingDays = BigDecimal("5.75"),
                                recordsQuantity = 4,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                                squareFootage = getMetricDetail(),
                            ),
                        ),
                ),
            )

        val result = runBlocking { handler.invoke(input) }
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should handle unit mix request with success`() {
        val input =
            GetPropertyMetricsHandler.Input(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                metricType = MetricType.UNIT_MIX,
                unitCondition = null,
            )

        val mockedResponse =
            listOf(
                UnitMixMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            UnitMixRentMetricsOutput(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                recordsQuantity = 5,
                                unitsAvailable = 0,
                                averageListingDays = BigDecimal("0.20"),
                                askingRent = getMetricDetailOutput(),
                                deposit = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                effectiveRent = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                squareFootage = getMetricDetailOutput(),
                                totalUnits = 9,
                            ),
                            UnitMixRentMetricsOutput(
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                recordsQuantity = 4,
                                unitsAvailable = 0,
                                averageListingDays = BigDecimal("5.75"),
                                askingRent = getMetricDetailOutput(),
                                deposit = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                effectiveRent = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                squareFootage = getMetricDetailOutput(),
                                totalUnits = 9,
                            ),
                        ),
                ),
            )

        val useCase = mockk<ComputeAggregatedMetricUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse
        val handler = GetPropertyMetricsHandler(BaseUseCaseExecutor, useCase)

        val expectedResponse =
            listOf(
                UnitMixRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            UnitMixRentMetric(
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                askingRent = getMetricDetail(),
                                deposit = getMetricDetail(),
                                averageListingDays = BigDecimal("0.20"),
                                recordsQuantity = 5,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                                squareFootage = getMetricDetail(),
                            ),
                            UnitMixRentMetric(
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                askingRent = getMetricDetail(),
                                deposit = getMetricDetail(),
                                averageListingDays = BigDecimal("5.75"),
                                recordsQuantity = 4,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                                squareFootage = getMetricDetail(),
                            ),
                        ),
                ),
            )

        val result = runBlocking { handler.invoke(input) }
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    @Test
    fun `should handle floor plan request with success`() {
        val input =
            GetPropertyMetricsHandler.Input(
                propertyIds = setOf("USTX-027626", "USTX-022222"),
                dateFrom = LocalDate.parse("2023-01-01"),
                dateTo = LocalDate.parse("2024-01-01"),
                metricType = MetricType.FLOOR_PLAN,
                unitCondition = null,
            )

        val mockedResponse =
            listOf(
                FloorPlanMetricsOutput(
                    id = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            FloorPlanRentMetricOutput(
                                floorPlan = "A2",
                                recordsQuantity = 5,
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = getMetricDetailOutput(),
                                unitsAvailable = 0,
                                averageListingDays = BigDecimal("0.20"),
                                askingRent = getMetricDetailOutput(),
                                deposit = getMetricDetailOutput(),
                                effectiveRent = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                totalUnits = 9,
                            ),
                            FloorPlanRentMetricOutput(
                                floorPlan = "A3",
                                recordsQuantity = 4,
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                squareFootage = getMetricDetailOutput(),
                                averageListingDays = BigDecimal("5.75"),
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetailOutput(),
                                askingRentPSF = getMetricDetailOutput(),
                                effectiveRentPSF = getMetricDetailOutput(),
                                askingRent = getMetricDetailOutput(),
                                deposit = null,
                            ),
                        ),
                ),
            )

        val useCase = mockk<ComputeAggregatedMetricUseCase>()
        coEvery { useCase.execute(any()) } returns mockedResponse
        val handler = GetPropertyMetricsHandler(BaseUseCaseExecutor, useCase)

        val expectedResponse =
            listOf(
                FloorPlanRentSummary(
                    propertyId = "USTX-027626",
                    dateFrom = LocalDate.parse("2023-01-01"),
                    dateTo = LocalDate.parse("2024-01-01"),
                    metrics =
                        listOf(
                            FloorPlanRentMetric(
                                floorPlan = "A2",
                                bedrooms = 2,
                                bathrooms = BigDecimal("1.5"),
                                squareFootage = getMetricDetail(),
                                askingRent = getMetricDetail(),
                                deposit = getMetricDetail(),
                                averageListingDays = BigDecimal("0.20"),
                                recordsQuantity = 5,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                            ),
                            FloorPlanRentMetric(
                                floorPlan = "A3",
                                bedrooms = 3,
                                bathrooms = BigDecimal("2"),
                                squareFootage = getMetricDetail(),
                                askingRent = getMetricDetail(),
                                deposit = null,
                                averageListingDays = BigDecimal("5.75"),
                                recordsQuantity = 4,
                                unitsAvailable = 0,
                                totalUnits = 9,
                                effectiveRent = getMetricDetail(),
                                askingRentPSF = getMetricDetail(),
                                effectiveRentPSF = getMetricDetail(),
                            ),
                        ),
                ),
            )

        val result = runBlocking { handler.invoke(input) }
        assertNotNull(result)
        assertEquals(expectedResponse, result)
    }

    private fun getMetricDetail() =
        MetricDetail(
            min = BigDecimal("600.00"),
            max = BigDecimal("600.00"),
            average = BigDecimal("600.00"),
            median = BigDecimal("600.00"),
        )

    private fun getMetricDetailOutput() =
        MetricDetailOutput(
            min = BigDecimal("600.00"),
            max = BigDecimal("600.00"),
            average = BigDecimal("600.00"),
            median = BigDecimal("600.00"),
        )
}

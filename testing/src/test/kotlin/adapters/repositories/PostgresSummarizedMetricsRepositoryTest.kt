package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import com.keyway.core.ports.repositories.SummarizedMetricsRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.MockedEntityFactory
import utils.MockedEntityFactory.buildMultifamilyProperty
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertTrue

class PostgresSummarizedMetricsRepositoryTest : BaseApplicationTest() {
    private val summarizedMetricsRepo: SummarizedMetricsRepository by inject()
    private val listingRepo: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()

    private lateinit var propertyId: String
    private lateinit var testDate: LocalDate

    @BeforeEach
    fun setup() {
        propertyId = "USFL-014695"
        testDate = LocalDate.now()

        // Setup test data
        getUnits(propertyId).forEach(propUnitRepo::saveOrUpdate)
        getListings(propertyId).forEach { listing ->
            listingRepo.save(listing)
            effectiveRentRepository.save(
                MockedEntityFactory.buildEffectiveRent(
                    listing = listing,
                    rent = listing.rent.multiply(Money.of("0.9")),
                    dateFrom = listing.dateFrom,
                    dateTo = listing.dateTo,
                ),
            )
        }

        createProperty(buildMultifamilyProperty(propertyId))
        // Create and refresh materialized views required by PostgresSummarizedMetricsRepository
        createAndRefreshMaterializedViews()
    }

    @Test
    fun `should handle MSA ID type`() {
        val msaCode = "19100"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.msaAndZipAggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(msaCode),
                        idType = IdType.MSA,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        // Should return results for MSA-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
    }

    @Test
    fun `should handle ZIP_CODE ID type`() {
        val zipCode = "75217"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.msaAndZipAggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(zipCode),
                        idType = IdType.ZIP_CODE,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BY_ID,
                    ),
                )
            }

        // Should return results for ZIP code-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
    }

    @Test
    fun `should handle MSA ID type by bedrooms`() {
        val msaCode = "19100"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.msaAndZipAggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(msaCode),
                        idType = IdType.MSA,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BEDROOMS,
                    ),
                )
            }

        // Should return results for MSA-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
    }

    @Test
    fun `should handle ZIP_CODE ID type by bedrooms`() {
        val zipCode = "75217"
        val metrics =
            runBlocking {
                summarizedMetricsRepo.msaAndZipAggregateMetrics(
                    MetricsFiltersQuery(
                        ids = setOf(zipCode),
                        idType = IdType.ZIP_CODE,
                        dateFrom = testDate,
                        dateTo = testDate,
                        type = MetricType.BEDROOMS,
                    ),
                )
            }

        // Should return results for ZIP code-based queries
        assertTrue(metrics.isNotEmpty())
        val metric = metrics.first()
    }

    private fun getUnits(propertyId: String) =
        listOf(
            PropertyUnit(
                propertyId = propertyId,
                unitId = "101",
                squareFootage = BigDecimal("850"),
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "A1",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal("0.3"),
                renovated = false,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "102",
                squareFootage = BigDecimal("950"),
                bedrooms = 2,
                bathrooms = BigDecimal("1.5"),
                floorPlan = "B1",
                amenities = setOf("BALCONY"),
                renovationProbability = BigDecimal("0.7"),
                renovated = true,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "103",
                squareFootage = BigDecimal("1200"),
                bedrooms = 3,
                bathrooms = BigDecimal("2.0"),
                floorPlan = "C1",
                amenities = setOf("BALCONY", "FIREPLACE"),
                renovationProbability = BigDecimal("0.5"),
                renovated = false,
            ),
        )

    private fun getListings(propertyId: String): List<RentListing> =
        getUnits(propertyId)
            .map { unit ->
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    typeId = unit.unitId,
                    type = RentListingType.UNIT,
                    dateFrom = testDate,
                    dateTo = testDate,
                    rent = Money.of(1000 + (unit.bedrooms * 200)), // Vary rent by bedroom count
                    unitSquareFootage = unit.squareFootage,
                    bedroomsQuantity = unit.bedrooms,
                    bathroomsQuantity = unit.bathrooms,
                    floorPlan = unit.floorPlan,
                )
            }.plus(
                // Add some floor plan listings
                listOf(
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "A1",
                        type = RentListingType.FLOOR_PLAN,
                        dateFrom = testDate,
                        dateTo = testDate,
                        rent = Money.of(1100),
                        bedroomsQuantity = 1,
                        bathroomsQuantity = BigDecimal.ONE,
                        floorPlan = "A1",
                    ),
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "B1",
                        type = RentListingType.FLOOR_PLAN,
                        dateFrom = testDate,
                        dateTo = testDate,
                        rent = Money.of(1300),
                        bedroomsQuantity = 2,
                        bathroomsQuantity = BigDecimal("1.5"),
                        floorPlan = "B1",
                    ),
                ),
            )
}

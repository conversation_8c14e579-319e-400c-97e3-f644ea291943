package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.entities.EffectiveRent
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.utils.DateUtils
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

class EffectiveRentRepositoryTest : BaseApplicationTest() {
    private val listingRepo: ListingsRepository by inject()
    private val repo: EffectiveRentRepository by inject()

    private fun createListing() =
        RentListing(
            id = UUID.randomUUID().toString(),
            propertyId = "USFL-014695",
            type = RentListingType.UNIT,
            typeId = "9-305",
            rent = Money.of("2335"),
            dateFrom = LocalDate.now(),
            dateTo = LocalDate.now(),
            recordSource = "apartments",
            zipCode = "33009",
            msaCode = "35620",
            unitSquareFootage = BigDecimal("1119"),
            bedroomsQuantity = 2,
            bathroomsQuantity = BigDecimal("1"),
            floorPlan = "A2",
            availableIn = null,
            rentDeposit = Money.of("600"),
            createdAt = DateUtils.now(),
            updateAt = DateUtils.now(),
        ).also {
            listingRepo.save(it)
        }

    @Test
    fun `could save and update a effective rent, also get`() {
        val listing = createListing()

        val effective =
            EffectiveRent.build(
                id = UUID.randomUUID().toString(),
                listing = listing,
                concessionIds = emptyList(),
                dateFrom = listing.dateFrom,
                dateTo = listing.dateTo,
                rent = listing.rent,
                rentDeposit = listing.rentDeposit,
                createdAt = listing.createdAt,
                updateAt = listing.updateAt,
                concessions = "THIS IS A MEGA CONCESSION",
            )

        repo.save(effective)

        val savedEffective = repo.getById(effective.id)

        Assertions.assertEquals(savedEffective.id, effective.id)
        Assertions.assertEquals(savedEffective.rentListingId, effective.rentListingId)
        Assertions.assertEquals(savedEffective.dateTo, effective.dateTo)
        Assertions.assertEquals(savedEffective.dateFrom, effective.dateFrom)
        Assertions.assertEquals(savedEffective.concessionIds, effective.concessionIds)
        Assertions.assertEquals(savedEffective.rent, effective.rent)

        Assertions.assertEquals(
            savedEffective,
            repo.getByListingId(listing.id).firstOrNull(),
        )
    }
}

package adapters.repositories

import application.utils.base.BaseApplicationTest
import com.keyway.core.dto.query.metrics.IdType
import com.keyway.core.dto.query.metrics.MetricsFiltersQuery
import com.keyway.core.entities.Money
import com.keyway.core.entities.RentListing
import com.keyway.core.entities.RentListingType
import com.keyway.core.entities.UnitCondition
import com.keyway.core.entities.metric.MetricType
import com.keyway.core.entities.property.PropertyUnit
import com.keyway.core.ports.repositories.EffectiveRentRepository
import com.keyway.core.ports.repositories.ListingsRepository
import com.keyway.core.ports.repositories.MetricsRepository
import com.keyway.core.ports.repositories.PropertyUnitRepository
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.koin.core.component.inject
import utils.MockedEntityFactory
import java.math.BigDecimal
import java.time.LocalDate
import kotlin.test.assertEquals

class PostgresMetricsRepositoryTest : BaseApplicationTest() {
    private val metricRepo: MetricsRepository by inject()
    private val listingRepo: ListingsRepository by inject()
    private val effectiveRentRepository: EffectiveRentRepository by inject()
    private val propUnitRepo: PropertyUnitRepository by inject()

    private lateinit var propertyId: String

    @BeforeEach
    fun setup() {
        propertyId = "USFL-014695"
        getUnits(propertyId).forEach(propUnitRepo::saveOrUpdate)
        getListings(propertyId).forEach {
            listingRepo.save(it)
            effectiveRentRepository.save(
                MockedEntityFactory.buildEffectiveRent(
                    listing = it,
                    rent = it.rent,
                    dateFrom = it.dateFrom,
                    dateTo = it.dateTo,
                ),
            )
        }
    }

    @Test
    fun `could get ALL metrics`() {
        val metrics =
            runBlocking {
                metricRepo.aggregateMetrics(
                    metricsFiltersQuery =
                        MetricsFiltersQuery(
                            ids = setOf(propertyId),
                            idType = IdType.PROPERTY,
                            dateFrom = LocalDate.now(),
                            dateTo = LocalDate.now(),
                            type = MetricType.BY_ID,
                        ),
                )
            }

        assertEquals(1, metrics.size)
        assertEquals(4, metrics.first().totalUnits)
        assertEquals(5, metrics.first().totalRecords)
    }

    @Test
    fun `could get RENO metrics`() {
        val metrics =
            runBlocking {
                metricRepo.aggregateMetrics(
                    metricsFiltersQuery =
                        MetricsFiltersQuery(
                            ids = setOf(propertyId),
                            idType = IdType.PROPERTY,
                            dateFrom = LocalDate.now(),
                            dateTo = LocalDate.now(),
                            type = MetricType.BY_ID,
                            unitCondition = UnitCondition.RENO,
                        ),
                )
            }

        assertEquals(1, metrics.size)
        assertEquals(1, metrics.first().totalUnits)
        assertEquals(1, metrics.first().totalRecords)
    }

    @Test
    fun `could get NON_RENO metrics`() {
        val metrics =
            runBlocking {
                metricRepo.aggregateMetrics(
                    metricsFiltersQuery =
                        MetricsFiltersQuery(
                            ids = setOf(propertyId),
                            idType = IdType.PROPERTY,
                            dateFrom = LocalDate.now(),
                            dateTo = LocalDate.now(),
                            type = MetricType.BY_ID,
                            unitCondition = UnitCondition.NON_RENO,
                        ),
                )
            }

        assertEquals(1, metrics.size)
        assertEquals(3, metrics.first().totalUnits)
        assertEquals(4, metrics.first().totalRecords)
    }

    private fun getUnits(propertyId: String) =
        listOf(
            PropertyUnit(
                propertyId = propertyId,
                unitId = "101",
                squareFootage = BigDecimal.TEN,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "CC",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal(0.6),
                renovated = false,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "102",
                squareFootage = BigDecimal.TEN,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "CC",
                amenities = setOf("NONE"),
                renovationProbability = BigDecimal(0.9),
                renovated = true,
            ),
            PropertyUnit(
                propertyId = propertyId,
                unitId = "103",
                squareFootage = BigDecimal.TEN,
                bedrooms = 1,
                bathrooms = BigDecimal.ONE,
                floorPlan = "CC",
                amenities = setOf("NONE"),
                renovationProbability = null,
                renovated = null,
            ),
        )

    private fun getListings(propertyId: String): List<RentListing> =
        getUnits(propertyId)
            .map {
                MockedEntityFactory.buildRentListing(
                    propertyId = propertyId,
                    typeId = it.unitId,
                    type = RentListingType.UNIT,
                    dateFrom = LocalDate.now(),
                    dateTo = LocalDate.now(),
                    rent = Money.of(100),
                )
            }.plus(
                listOf(
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "100",
                        type = RentListingType.UNIT,
                        dateFrom = LocalDate.now(),
                        dateTo = LocalDate.now(),
                        rent = Money.of(100),
                    ),
                    MockedEntityFactory.buildRentListing(
                        propertyId = propertyId,
                        typeId = "CC",
                        type = RentListingType.FLOOR_PLAN,
                        dateFrom = LocalDate.now(),
                        dateTo = LocalDate.now(),
                        rent = Money.of(100),
                    ),
                ),
            )
}
